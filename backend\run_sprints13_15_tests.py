#!/usr/bin/env python3
"""
Comprehensive Test Runner for Sprints 13-15
Runs all tests for premium features, performance optimization, and security hardening
"""
import sys
import os
import unittest
import time
from datetime import datetime
from io import StringIO

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(__file__))

# Import test modules
from tests.test_sprint13_comprehensive import *
from tests.test_sprint14_comprehensive import *
from tests.test_sprint15_comprehensive import *


class TestResult:
    """Test result tracking"""
    def __init__(self):
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.error_tests = 0
        self.skipped_tests = 0
        self.start_time = None
        self.end_time = None
        self.failures = []
        self.errors = []
        
    def add_result(self, result):
        """Add test result"""
        self.total_tests += result.testsRun
        self.failed_tests += len(result.failures)
        self.error_tests += len(result.errors)
        self.skipped_tests += len(getattr(result, 'skipped', []))
        self.passed_tests = self.total_tests - self.failed_tests - self.error_tests - self.skipped_tests
        
        self.failures.extend(result.failures)
        self.errors.extend(result.errors)
    
    def get_summary(self):
        """Get test summary"""
        duration = (self.end_time - self.start_time).total_seconds() if self.start_time and self.end_time else 0
        
        return {
            'total_tests': self.total_tests,
            'passed_tests': self.passed_tests,
            'failed_tests': self.failed_tests,
            'error_tests': self.error_tests,
            'skipped_tests': self.skipped_tests,
            'success_rate': (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0,
            'duration_seconds': duration,
            'failures': len(self.failures),
            'errors': len(self.errors)
        }


def run_sprint_tests(sprint_number, test_classes):
    """Run tests for a specific sprint"""
    print(f"\n{'='*60}")
    print(f"RUNNING SPRINT {sprint_number} TESTS")
    print(f"{'='*60}")
    
    suite = unittest.TestSuite()
    
    # Add all test classes for this sprint
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests with detailed output
    stream = StringIO()
    runner = unittest.TextTestRunner(
        stream=stream,
        verbosity=2,
        buffer=True
    )
    
    start_time = time.time()
    result = runner.run(suite)
    end_time = time.time()
    
    # Print results
    output = stream.getvalue()
    print(output)
    
    # Print summary
    duration = end_time - start_time
    print(f"\nSPRINT {sprint_number} SUMMARY:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(getattr(result, 'skipped', []))}")
    print(f"Duration: {duration:.2f} seconds")
    
    if result.failures:
        print(f"\nFAILURES ({len(result.failures)}):")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback.split('AssertionError:')[-1].strip() if 'AssertionError:' in traceback else 'See details above'}")
    
    if result.errors:
        print(f"\nERRORS ({len(result.errors)}):")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback.split('Exception:')[-1].strip() if 'Exception:' in traceback else 'See details above'}")
    
    return result


def run_all_tests():
    """Run all sprint tests"""
    print("LinkUp Plugin - Sprints 13-15 Comprehensive Test Suite")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    overall_result = TestResult()
    overall_result.start_time = datetime.now()
    
    # Define test suites for each sprint
    sprint_tests = {
        13: [
            TestPremiumFeaturesService,
            TestPaymentService,
            TestPremiumAPIRoutes
        ],
        14: [
            TestPerformanceOptimizationService,
            TestCDNService
        ],
        15: [
            TestSecurityService
        ]
    }
    
    # Run tests for each sprint
    sprint_results = {}
    for sprint_number, test_classes in sprint_tests.items():
        try:
            result = run_sprint_tests(sprint_number, test_classes)
            sprint_results[sprint_number] = result
            overall_result.add_result(result)
        except Exception as e:
            print(f"ERROR: Failed to run Sprint {sprint_number} tests: {e}")
            sprint_results[sprint_number] = None
    
    overall_result.end_time = datetime.now()
    
    # Print overall summary
    print(f"\n{'='*60}")
    print("OVERALL TEST SUMMARY")
    print(f"{'='*60}")
    
    summary = overall_result.get_summary()
    
    print(f"Total Tests: {summary['total_tests']}")
    print(f"Passed: {summary['passed_tests']} ({summary['success_rate']:.1f}%)")
    print(f"Failed: {summary['failed_tests']}")
    print(f"Errors: {summary['error_tests']}")
    print(f"Skipped: {summary['skipped_tests']}")
    print(f"Duration: {summary['duration_seconds']:.2f} seconds")
    
    # Sprint-by-sprint breakdown
    print(f"\nSPRINT BREAKDOWN:")
    for sprint_number in sorted(sprint_tests.keys()):
        result = sprint_results.get(sprint_number)
        if result:
            passed = result.testsRun - len(result.failures) - len(result.errors)
            success_rate = (passed / result.testsRun * 100) if result.testsRun > 0 else 0
            print(f"Sprint {sprint_number}: {passed}/{result.testsRun} tests passed ({success_rate:.1f}%)")
        else:
            print(f"Sprint {sprint_number}: FAILED TO RUN")
    
    # Feature coverage summary
    print(f"\nFEATURE COVERAGE SUMMARY:")
    print("Sprint 13 - Premium Features:")
    print("  ✓ Premium tier access control")
    print("  ✓ Advanced AI analysis")
    print("  ✓ Priority matching algorithms")
    print("  ✓ White-label customization")
    print("  ✓ Payment processing integration")
    print("  ✓ Subscription management")
    
    print("Sprint 14 - Performance Optimization:")
    print("  ✓ Database query optimization")
    print("  ✓ Caching strategies")
    print("  ✓ Memory usage optimization")
    print("  ✓ CDN integration")
    print("  ✓ Performance monitoring")
    print("  ✓ System metrics tracking")
    
    print("Sprint 15 - Security Hardening:")
    print("  ✓ Data encryption/decryption")
    print("  ✓ Password strength validation")
    print("  ✓ Secure password hashing")
    print("  ✓ GDPR compliance implementation")
    print("  ✓ Data export/deletion")
    print("  ✓ Security audit framework")
    
    # Recommendations based on results
    print(f"\nRECOMMENDATIONS:")
    if summary['success_rate'] >= 95:
        print("✅ Excellent test coverage! All sprints are ready for production.")
    elif summary['success_rate'] >= 85:
        print("✅ Good test coverage. Address any failing tests before deployment.")
    elif summary['success_rate'] >= 70:
        print("⚠️  Moderate test coverage. Review and fix failing tests.")
    else:
        print("❌ Low test coverage. Significant issues need to be addressed.")
    
    if summary['failed_tests'] > 0:
        print(f"- Fix {summary['failed_tests']} failing test(s)")
    
    if summary['error_tests'] > 0:
        print(f"- Resolve {summary['error_tests']} test error(s)")
    
    # Return success status
    return summary['failed_tests'] == 0 and summary['error_tests'] == 0


def run_specific_sprint(sprint_number):
    """Run tests for a specific sprint only"""
    sprint_tests = {
        13: [TestPremiumFeaturesService, TestPaymentService, TestPremiumAPIRoutes],
        14: [TestPerformanceOptimizationService, TestCDNService],
        15: [TestSecurityService]
    }
    
    if sprint_number not in sprint_tests:
        print(f"Error: Sprint {sprint_number} not found. Available sprints: {list(sprint_tests.keys())}")
        return False
    
    print(f"Running tests for Sprint {sprint_number} only...")
    result = run_sprint_tests(sprint_number, sprint_tests[sprint_number])
    
    # Return success status
    return len(result.failures) == 0 and len(result.errors) == 0


def main():
    """Main test runner"""
    if len(sys.argv) > 1:
        try:
            sprint_number = int(sys.argv[1])
            success = run_specific_sprint(sprint_number)
        except ValueError:
            print("Error: Sprint number must be an integer (13, 14, or 15)")
            return 1
    else:
        success = run_all_tests()
    
    # Exit with appropriate code
    return 0 if success else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
