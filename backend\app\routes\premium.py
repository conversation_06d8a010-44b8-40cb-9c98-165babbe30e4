"""
Premium Features API Routes for LinkUp Plugin
Handles premium feature access, subscriptions, and billing
"""
from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from functools import wraps
import logging

from app.services.premium_features_service import PremiumFeaturesService, PremiumTier
from app.services.payment_service import PaymentService
from app.utils.validators import validate_json
from app.utils.responses import success_response, error_response
from app.models.user import User

logger = logging.getLogger(__name__)

bp = Blueprint('premium', __name__)
premium_service = PremiumFeaturesService()
payment_service = PaymentService()


def premium_required(feature: str):
    """Decorator to require premium feature access"""
    def decorator(f):
        @wraps(f)
        @jwt_required()
        def decorated_function(*args, **kwargs):
            user_id = get_jwt_identity()
            try:
                premium_service.require_premium_feature(user_id, feature)
                return f(*args, **kwargs)
            except PermissionError as e:
                return error_response(str(e), 403)
        return decorated_function
    return decorator


@bp.route('/features', methods=['GET'])
@jwt_required()
def get_feature_access():
    """Get user's premium feature access"""
    try:
        user_id = get_jwt_identity()
        access = premium_service.get_feature_access(user_id)
        tier = premium_service.get_user_tier(user_id)
        
        return success_response({
            'tier': tier.value,
            'features': {
                'advanced_ai_analysis': access.advanced_ai_analysis,
                'priority_matching': access.priority_matching,
                'white_label_customization': access.white_label_customization,
                'advanced_reporting': access.advanced_reporting,
                'api_access': access.api_access,
                'custom_integrations': access.custom_integrations,
                'dedicated_support': access.dedicated_support,
                'bulk_operations': access.bulk_operations,
                'advanced_filters': access.advanced_filters,
                'competitor_intelligence': access.competitor_intelligence
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting feature access: {e}")
        return error_response("Failed to get feature access", 500)


@bp.route('/analysis/advanced', methods=['POST'])
@premium_required('advanced_ai_analysis')
def advanced_analysis():
    """Perform advanced AI content analysis"""
    try:
        user_id = get_jwt_identity()
        data = validate_json(request, required_fields=['content'])
        
        content = data['content']
        analysis_type = data.get('analysis_type', 'comprehensive')
        
        if len(content) > 50000:  # 50KB limit
            return error_response("Content too large for analysis", 400)
        
        result = premium_service.get_advanced_ai_analysis(
            user_id, content, analysis_type
        )
        
        return success_response({
            'analysis': result,
            'analysis_type': analysis_type,
            'content_length': len(content),
            'processing_time': 'N/A'  # Would be calculated in real implementation
        })
        
    except PermissionError as e:
        return error_response(str(e), 403)
    except Exception as e:
        logger.error(f"Error in advanced analysis: {e}")
        return error_response("Analysis failed", 500)


@bp.route('/matching/priority', methods=['POST'])
@premium_required('priority_matching')
def priority_matching():
    """Get priority matching results"""
    try:
        user_id = get_jwt_identity()
        data = validate_json(request, required_fields=['website_id'])
        
        website_id = data['website_id']
        limit = min(data.get('limit', 50), 100)  # Max 100 results
        
        matches = premium_service.get_priority_matching(user_id, website_id, limit)
        
        return success_response({
            'matches': matches,
            'total_matches': len(matches),
            'website_id': website_id,
            'priority_algorithm': 'advanced_v2'
        })
        
    except PermissionError as e:
        return error_response(str(e), 403)
    except Exception as e:
        logger.error(f"Error in priority matching: {e}")
        return error_response("Priority matching failed", 500)


@bp.route('/white-label/config', methods=['GET'])
@premium_required('white_label_customization')
def get_white_label_config():
    """Get white-label configuration"""
    try:
        user_id = get_jwt_identity()
        config = premium_service.get_white_label_config(user_id)
        
        return success_response({
            'config': config,
            'customization_options': [
                'branding',
                'domain',
                'features',
                'integrations'
            ]
        })
        
    except PermissionError as e:
        return error_response(str(e), 403)
    except Exception as e:
        logger.error(f"Error getting white-label config: {e}")
        return error_response("Failed to get configuration", 500)


@bp.route('/white-label/config', methods=['PUT'])
@premium_required('white_label_customization')
def update_white_label_config():
    """Update white-label configuration"""
    try:
        user_id = get_jwt_identity()
        data = validate_json(request)
        
        # Validate configuration data
        allowed_fields = ['branding', 'domain', 'features', 'integrations']
        config_updates = {k: v for k, v in data.items() if k in allowed_fields}
        
        if not config_updates:
            return error_response("No valid configuration fields provided", 400)
        
        # In real implementation, would save to database
        # For now, return success with updated config
        current_config = premium_service.get_white_label_config(user_id)
        
        # Merge updates
        for key, value in config_updates.items():
            if key in current_config:
                current_config[key].update(value)
        
        return success_response({
            'message': 'Configuration updated successfully',
            'config': current_config
        })
        
    except PermissionError as e:
        return error_response(str(e), 403)
    except Exception as e:
        logger.error(f"Error updating white-label config: {e}")
        return error_response("Failed to update configuration", 500)


# Subscription Management Routes

@bp.route('/subscription/plans', methods=['GET'])
def get_pricing_plans():
    """Get available pricing plans"""
    try:
        plans = payment_service.get_pricing_plans()
        
        # Convert to API response format
        plans_data = {}
        for plan_id, plan in plans.items():
            plans_data[plan_id] = {
                'name': plan.name,
                'price_monthly': plan.price_monthly,
                'price_yearly': plan.price_yearly,
                'features': plan.features,
                'limits': plan.limits,
                'savings_yearly': round((plan.price_monthly * 12 - plan.price_yearly), 2)
            }
        
        return success_response({
            'plans': plans_data,
            'currency': 'USD',
            'billing_cycles': ['monthly', 'yearly']
        })
        
    except Exception as e:
        logger.error(f"Error getting pricing plans: {e}")
        return error_response("Failed to get pricing plans", 500)


@bp.route('/subscription/create', methods=['POST'])
@jwt_required()
def create_subscription():
    """Create new subscription"""
    try:
        user_id = get_jwt_identity()
        data = validate_json(request, required_fields=['plan_id'])
        
        plan_id = data['plan_id']
        billing_cycle = data.get('billing_cycle', 'monthly')
        
        if billing_cycle not in ['monthly', 'yearly']:
            return error_response("Invalid billing cycle", 400)
        
        result = payment_service.create_subscription(user_id, plan_id, billing_cycle)
        
        if result.success:
            return success_response({
                'message': 'Subscription created successfully',
                'subscription_id': result.subscription_id,
                'transaction_id': result.transaction_id,
                'next_billing_date': result.next_billing_date.isoformat() if result.next_billing_date else None
            })
        else:
            return error_response(result.error_message, 400)
        
    except Exception as e:
        logger.error(f"Error creating subscription: {e}")
        return error_response("Failed to create subscription", 500)


@bp.route('/subscription/status', methods=['GET'])
@jwt_required()
def get_subscription_status():
    """Get current subscription status"""
    try:
        user_id = get_jwt_identity()
        status = payment_service.get_subscription_status(user_id)
        
        return success_response({
            'subscription': status
        })
        
    except Exception as e:
        logger.error(f"Error getting subscription status: {e}")
        return error_response("Failed to get subscription status", 500)


@bp.route('/subscription/cancel', methods=['POST'])
@jwt_required()
def cancel_subscription():
    """Cancel current subscription"""
    try:
        user_id = get_jwt_identity()
        result = payment_service.cancel_subscription(user_id)
        
        if result.success:
            return success_response({
                'message': 'Subscription cancelled successfully',
                'subscription_id': result.subscription_id
            })
        else:
            return error_response(result.error_message, 400)
        
    except Exception as e:
        logger.error(f"Error cancelling subscription: {e}")
        return error_response("Failed to cancel subscription", 500)


@bp.route('/subscription/update', methods=['PUT'])
@jwt_required()
def update_subscription():
    """Update subscription plan"""
    try:
        user_id = get_jwt_identity()
        data = validate_json(request, required_fields=['new_plan_id'])
        
        new_plan_id = data['new_plan_id']
        result = payment_service.update_subscription(user_id, new_plan_id)
        
        if result.success:
            return success_response({
                'message': 'Subscription updated successfully',
                'subscription_id': result.subscription_id,
                'next_billing_date': result.next_billing_date.isoformat() if result.next_billing_date else None
            })
        else:
            return error_response(result.error_message, 400)
        
    except Exception as e:
        logger.error(f"Error updating subscription: {e}")
        return error_response("Failed to update subscription", 500)


@bp.route('/webhook/stripe', methods=['POST'])
def stripe_webhook():
    """Handle Stripe webhook events"""
    try:
        payload = request.get_data(as_text=True)
        signature = request.headers.get('Stripe-Signature')
        
        if not signature:
            return error_response("Missing signature", 400)
        
        result = payment_service.handle_webhook(payload, signature)
        
        return success_response({
            'status': result['status'],
            'message': result.get('message', 'Webhook processed')
        })
        
    except Exception as e:
        logger.error(f"Error processing webhook: {e}")
        return error_response("Webhook processing failed", 500)


# Usage and Analytics Routes

@bp.route('/usage/summary', methods=['GET'])
@jwt_required()
def get_usage_summary():
    """Get premium feature usage summary"""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return error_response("User not found", 404)
        
        # Get usage data
        usage = user.get_usage_this_month()
        limits = user.get_plan_limits()
        
        # Calculate usage percentages
        usage_summary = {
            'current_plan': user.plan,
            'billing_period': 'monthly',  # Simplified
            'usage': {
                'backlinks_created': {
                    'used': usage['backlinks_created'],
                    'limit': limits['backlinks_per_month'],
                    'percentage': round((usage['backlinks_created'] / limits['backlinks_per_month']) * 100, 1)
                },
                'api_requests': {
                    'used': usage['api_requests'],
                    'limit': limits['api_requests_per_hour'] * 24 * 30,  # Monthly estimate
                    'percentage': round((usage['api_requests'] / (limits['api_requests_per_hour'] * 24 * 30)) * 100, 1)
                },
                'content_analyzed': {
                    'used': usage['content_analyzed'],
                    'limit': 'unlimited' if user.plan != 'free' else 100,
                    'percentage': 0 if user.plan != 'free' else round((usage['content_analyzed'] / 100) * 100, 1)
                }
            },
            'features_used': {
                'advanced_analysis': premium_service.has_feature_access(user_id, 'advanced_ai_analysis'),
                'priority_matching': premium_service.has_feature_access(user_id, 'priority_matching'),
                'white_label': premium_service.has_feature_access(user_id, 'white_label_customization')
            }
        }
        
        return success_response({
            'usage_summary': usage_summary
        })
        
    except Exception as e:
        logger.error(f"Error getting usage summary: {e}")
        return error_response("Failed to get usage summary", 500)
