"""
Payment Processing Service for LinkUp Plugin
Handles subscription management, billing, and payment processing
"""
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import stripe
import os

from app import db
from app.models.user import User

logger = logging.getLogger(__name__)

# Configure Stripe
stripe.api_key = os.getenv('STRIPE_SECRET_KEY')


class SubscriptionStatus(Enum):
    """Subscription status options"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    CANCELLED = "cancelled"
    PAST_DUE = "past_due"
    TRIALING = "trialing"
    UNPAID = "unpaid"


class PaymentMethod(Enum):
    """Payment method options"""
    CREDIT_CARD = "credit_card"
    PAYPAL = "paypal"
    BANK_TRANSFER = "bank_transfer"
    CRYPTO = "crypto"


@dataclass
class PricingPlan:
    """Pricing plan configuration"""
    plan_id: str
    name: str
    price_monthly: float
    price_yearly: float
    features: List[str]
    limits: Dict[str, int]
    stripe_price_id_monthly: str
    stripe_price_id_yearly: str


@dataclass
class PaymentResult:
    """Payment processing result"""
    success: bool
    transaction_id: Optional[str] = None
    error_message: Optional[str] = None
    subscription_id: Optional[str] = None
    next_billing_date: Optional[datetime] = None


class PaymentService:
    """Service for handling payments and subscriptions"""
    
    def __init__(self):
        self.pricing_plans = self._initialize_pricing_plans()
        self.webhook_secret = os.getenv('STRIPE_WEBHOOK_SECRET')
    
    def _initialize_pricing_plans(self) -> Dict[str, PricingPlan]:
        """Initialize pricing plans configuration"""
        return {
            'pro': PricingPlan(
                plan_id='pro',
                name='Pro',
                price_monthly=29.99,
                price_yearly=299.99,
                features=[
                    'Advanced AI Analysis',
                    'Priority Matching',
                    'Advanced Reporting',
                    'API Access',
                    'Email Support'
                ],
                limits={
                    'backlinks_per_month': 100,
                    'websites': 5,
                    'api_requests_per_hour': 1000
                },
                stripe_price_id_monthly='price_pro_monthly',
                stripe_price_id_yearly='price_pro_yearly'
            ),
            'agency': PricingPlan(
                plan_id='agency',
                name='Agency',
                price_monthly=99.99,
                price_yearly=999.99,
                features=[
                    'All Pro features',
                    'White-label Customization',
                    'Bulk Operations',
                    'Priority Support',
                    'Custom Integrations'
                ],
                limits={
                    'backlinks_per_month': 1000,
                    'websites': 50,
                    'api_requests_per_hour': 10000
                },
                stripe_price_id_monthly='price_agency_monthly',
                stripe_price_id_yearly='price_agency_yearly'
            ),
            'enterprise': PricingPlan(
                plan_id='enterprise',
                name='Enterprise',
                price_monthly=299.99,
                price_yearly=2999.99,
                features=[
                    'All Agency features',
                    'Dedicated Support',
                    'Custom Development',
                    'SLA Guarantee',
                    'Advanced Security'
                ],
                limits={
                    'backlinks_per_month': 10000,
                    'websites': 500,
                    'api_requests_per_hour': 100000
                },
                stripe_price_id_monthly='price_enterprise_monthly',
                stripe_price_id_yearly='price_enterprise_yearly'
            )
        }
    
    def get_pricing_plans(self) -> Dict[str, PricingPlan]:
        """Get all available pricing plans"""
        return self.pricing_plans
    
    def get_plan_details(self, plan_id: str) -> Optional[PricingPlan]:
        """Get details for specific pricing plan"""
        return self.pricing_plans.get(plan_id)
    
    def create_customer(self, user_id: int, email: str, name: str = None) -> str:
        """Create Stripe customer"""
        try:
            customer = stripe.Customer.create(
                email=email,
                name=name,
                metadata={'user_id': str(user_id)}
            )
            
            # Update user with Stripe customer ID
            user = User.query.get(user_id)
            if user:
                user.stripe_customer_id = customer.id
                db.session.commit()
            
            logger.info(f"Created Stripe customer {customer.id} for user {user_id}")
            return customer.id
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create Stripe customer: {e}")
            raise Exception(f"Payment system error: {e}")
    
    def create_subscription(self, user_id: int, plan_id: str, 
                          billing_cycle: str = 'monthly') -> PaymentResult:
        """Create new subscription"""
        try:
            user = User.query.get(user_id)
            if not user:
                return PaymentResult(success=False, error_message="User not found")
            
            plan = self.get_plan_details(plan_id)
            if not plan:
                return PaymentResult(success=False, error_message="Invalid plan")
            
            # Get or create Stripe customer
            customer_id = user.stripe_customer_id
            if not customer_id:
                customer_id = self.create_customer(
                    user_id, 
                    user.email, 
                    f"{user.first_name} {user.last_name}".strip()
                )
            
            # Get price ID based on billing cycle
            price_id = (plan.stripe_price_id_yearly if billing_cycle == 'yearly' 
                       else plan.stripe_price_id_monthly)
            
            # Create subscription
            subscription = stripe.Subscription.create(
                customer=customer_id,
                items=[{'price': price_id}],
                payment_behavior='default_incomplete',
                expand=['latest_invoice.payment_intent'],
                metadata={'user_id': str(user_id), 'plan_id': plan_id}
            )
            
            # Update user subscription
            user.plan = plan_id
            user.plan_expires_at = datetime.utcnow() + timedelta(
                days=365 if billing_cycle == 'yearly' else 30
            )
            db.session.commit()
            
            return PaymentResult(
                success=True,
                subscription_id=subscription.id,
                transaction_id=subscription.latest_invoice.payment_intent.id,
                next_billing_date=datetime.fromtimestamp(subscription.current_period_end)
            )
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create subscription: {e}")
            return PaymentResult(success=False, error_message=str(e))
        except Exception as e:
            logger.error(f"Unexpected error creating subscription: {e}")
            return PaymentResult(success=False, error_message="Internal error")
    
    def cancel_subscription(self, user_id: int) -> PaymentResult:
        """Cancel user subscription"""
        try:
            user = User.query.get(user_id)
            if not user or not user.stripe_customer_id:
                return PaymentResult(success=False, error_message="No active subscription")
            
            # Get active subscriptions
            subscriptions = stripe.Subscription.list(
                customer=user.stripe_customer_id,
                status='active'
            )
            
            if not subscriptions.data:
                return PaymentResult(success=False, error_message="No active subscription found")
            
            # Cancel the subscription
            subscription = subscriptions.data[0]
            cancelled_subscription = stripe.Subscription.delete(subscription.id)
            
            # Update user plan to free
            user.plan = 'free'
            user.plan_expires_at = None
            db.session.commit()
            
            logger.info(f"Cancelled subscription for user {user_id}")
            return PaymentResult(success=True, subscription_id=cancelled_subscription.id)
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to cancel subscription: {e}")
            return PaymentResult(success=False, error_message=str(e))
        except Exception as e:
            logger.error(f"Unexpected error cancelling subscription: {e}")
            return PaymentResult(success=False, error_message="Internal error")
    
    def update_subscription(self, user_id: int, new_plan_id: str) -> PaymentResult:
        """Update user subscription to new plan"""
        try:
            user = User.query.get(user_id)
            if not user or not user.stripe_customer_id:
                return PaymentResult(success=False, error_message="No active subscription")
            
            new_plan = self.get_plan_details(new_plan_id)
            if not new_plan:
                return PaymentResult(success=False, error_message="Invalid plan")
            
            # Get active subscription
            subscriptions = stripe.Subscription.list(
                customer=user.stripe_customer_id,
                status='active'
            )
            
            if not subscriptions.data:
                return PaymentResult(success=False, error_message="No active subscription found")
            
            subscription = subscriptions.data[0]
            
            # Update subscription
            updated_subscription = stripe.Subscription.modify(
                subscription.id,
                items=[{
                    'id': subscription['items']['data'][0].id,
                    'price': new_plan.stripe_price_id_monthly,
                }],
                proration_behavior='create_prorations'
            )
            
            # Update user plan
            user.plan = new_plan_id
            db.session.commit()
            
            return PaymentResult(
                success=True,
                subscription_id=updated_subscription.id,
                next_billing_date=datetime.fromtimestamp(updated_subscription.current_period_end)
            )
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to update subscription: {e}")
            return PaymentResult(success=False, error_message=str(e))
        except Exception as e:
            logger.error(f"Unexpected error updating subscription: {e}")
            return PaymentResult(success=False, error_message="Internal error")
    
    def get_subscription_status(self, user_id: int) -> Dict[str, Any]:
        """Get current subscription status for user"""
        try:
            user = User.query.get(user_id)
            if not user:
                return {'status': 'not_found', 'message': 'User not found'}
            
            if user.plan == 'free':
                return {
                    'status': 'free',
                    'plan': 'free',
                    'expires_at': None,
                    'is_active': True
                }
            
            if not user.stripe_customer_id:
                return {
                    'status': 'inactive',
                    'plan': user.plan,
                    'expires_at': user.plan_expires_at,
                    'is_active': False
                }
            
            # Get Stripe subscription
            subscriptions = stripe.Subscription.list(
                customer=user.stripe_customer_id,
                limit=1
            )
            
            if not subscriptions.data:
                return {
                    'status': 'inactive',
                    'plan': user.plan,
                    'expires_at': user.plan_expires_at,
                    'is_active': False
                }
            
            subscription = subscriptions.data[0]
            
            return {
                'status': subscription.status,
                'plan': user.plan,
                'expires_at': datetime.fromtimestamp(subscription.current_period_end),
                'is_active': subscription.status == 'active',
                'subscription_id': subscription.id,
                'next_billing_date': datetime.fromtimestamp(subscription.current_period_end),
                'billing_cycle': 'yearly' if subscription.items.data[0].price.recurring.interval == 'year' else 'monthly'
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to get subscription status: {e}")
            return {'status': 'error', 'message': str(e)}
        except Exception as e:
            logger.error(f"Unexpected error getting subscription status: {e}")
            return {'status': 'error', 'message': 'Internal error'}
    
    def handle_webhook(self, payload: str, signature: str) -> Dict[str, Any]:
        """Handle Stripe webhook events"""
        try:
            event = stripe.Webhook.construct_event(
                payload, signature, self.webhook_secret
            )
            
            # Handle different event types
            if event['type'] == 'invoice.payment_succeeded':
                return self._handle_payment_succeeded(event['data']['object'])
            elif event['type'] == 'invoice.payment_failed':
                return self._handle_payment_failed(event['data']['object'])
            elif event['type'] == 'customer.subscription.deleted':
                return self._handle_subscription_cancelled(event['data']['object'])
            elif event['type'] == 'customer.subscription.updated':
                return self._handle_subscription_updated(event['data']['object'])
            
            return {'status': 'ignored', 'event_type': event['type']}
            
        except ValueError as e:
            logger.error(f"Invalid webhook payload: {e}")
            return {'status': 'error', 'message': 'Invalid payload'}
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Invalid webhook signature: {e}")
            return {'status': 'error', 'message': 'Invalid signature'}
        except Exception as e:
            logger.error(f"Webhook processing error: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _handle_payment_succeeded(self, invoice: Dict[str, Any]) -> Dict[str, Any]:
        """Handle successful payment"""
        try:
            customer_id = invoice['customer']
            user = User.query.filter_by(stripe_customer_id=customer_id).first()
            
            if user:
                # Extend subscription period
                if user.plan_expires_at:
                    user.plan_expires_at = datetime.fromtimestamp(invoice['period_end'])
                else:
                    user.plan_expires_at = datetime.utcnow() + timedelta(days=30)
                
                db.session.commit()
                logger.info(f"Payment succeeded for user {user.id}")
            
            return {'status': 'processed', 'user_id': user.id if user else None}
            
        except Exception as e:
            logger.error(f"Error handling payment success: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _handle_payment_failed(self, invoice: Dict[str, Any]) -> Dict[str, Any]:
        """Handle failed payment"""
        try:
            customer_id = invoice['customer']
            user = User.query.filter_by(stripe_customer_id=customer_id).first()
            
            if user:
                # Could implement grace period or immediate downgrade
                logger.warning(f"Payment failed for user {user.id}")
                # For now, just log - could implement retry logic
            
            return {'status': 'processed', 'user_id': user.id if user else None}
            
        except Exception as e:
            logger.error(f"Error handling payment failure: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _handle_subscription_cancelled(self, subscription: Dict[str, Any]) -> Dict[str, Any]:
        """Handle subscription cancellation"""
        try:
            customer_id = subscription['customer']
            user = User.query.filter_by(stripe_customer_id=customer_id).first()
            
            if user:
                user.plan = 'free'
                user.plan_expires_at = None
                db.session.commit()
                logger.info(f"Subscription cancelled for user {user.id}")
            
            return {'status': 'processed', 'user_id': user.id if user else None}
            
        except Exception as e:
            logger.error(f"Error handling subscription cancellation: {e}")
            return {'status': 'error', 'message': str(e)}
    
    def _handle_subscription_updated(self, subscription: Dict[str, Any]) -> Dict[str, Any]:
        """Handle subscription update"""
        try:
            customer_id = subscription['customer']
            user = User.query.filter_by(stripe_customer_id=customer_id).first()
            
            if user:
                # Update plan based on subscription items
                # This is simplified - would need to map Stripe price IDs to plan IDs
                user.plan_expires_at = datetime.fromtimestamp(subscription['current_period_end'])
                db.session.commit()
                logger.info(f"Subscription updated for user {user.id}")
            
            return {'status': 'processed', 'user_id': user.id if user else None}
            
        except Exception as e:
            logger.error(f"Error handling subscription update: {e}")
            return {'status': 'error', 'message': str(e)}
