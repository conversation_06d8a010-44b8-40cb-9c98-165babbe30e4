"""
Performance Optimization Service for LinkUp Plugin
Implements database optimization, caching strategies, and performance monitoring
"""
import logging
import time
import psutil
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from functools import wraps
import redis
from sqlalchemy import text, func
from sqlalchemy.orm import joinedload

from app import db, cache
from app.models.user import User
from app.models.website import Website
from app.models.analysis import ContentAnalysis
from app.models.backlink import Backlink

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Performance metrics data structure"""
    timestamp: datetime
    response_time: float
    memory_usage: float
    cpu_usage: float
    database_queries: int
    cache_hit_rate: float
    active_connections: int


@dataclass
class OptimizationSuggestion:
    """Performance optimization suggestion"""
    category: str
    priority: str
    description: str
    impact: str
    implementation_effort: str
    estimated_improvement: float


class PerformanceOptimizationService:
    """Service for performance optimization and monitoring"""
    
    def __init__(self):
        self.redis_client = redis.Redis.from_url('redis://localhost:6379/0')
        self.performance_cache_timeout = 300  # 5 minutes
        self.metrics_history = []
        self.optimization_thresholds = {
            'response_time_warning': 1.0,  # seconds
            'response_time_critical': 3.0,  # seconds
            'memory_usage_warning': 80.0,  # percentage
            'memory_usage_critical': 95.0,  # percentage
            'cpu_usage_warning': 70.0,  # percentage
            'cpu_usage_critical': 90.0,  # percentage
            'cache_hit_rate_warning': 70.0,  # percentage
            'cache_hit_rate_critical': 50.0  # percentage
        }
    
    def monitor_performance(self, func):
        """Decorator to monitor function performance"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            
            try:
                result = func(*args, **kwargs)
                
                end_time = time.time()
                end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
                
                # Record performance metrics
                metrics = {
                    'function_name': func.__name__,
                    'execution_time': end_time - start_time,
                    'memory_delta': end_memory - start_memory,
                    'timestamp': datetime.utcnow().isoformat(),
                    'success': True
                }
                
                self._record_performance_metrics(metrics)
                return result
                
            except Exception as e:
                end_time = time.time()
                metrics = {
                    'function_name': func.__name__,
                    'execution_time': end_time - start_time,
                    'timestamp': datetime.utcnow().isoformat(),
                    'success': False,
                    'error': str(e)
                }
                self._record_performance_metrics(metrics)
                raise
                
        return wrapper
    
    def get_system_metrics(self) -> PerformanceMetrics:
        """Get current system performance metrics"""
        try:
            # CPU and memory metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # Database connection count
            db_connections = self._get_database_connection_count()
            
            # Cache hit rate
            cache_stats = self._get_cache_statistics()
            
            # Recent query count
            query_count = self._get_recent_query_count()
            
            metrics = PerformanceMetrics(
                timestamp=datetime.utcnow(),
                response_time=self._get_average_response_time(),
                memory_usage=memory.percent,
                cpu_usage=cpu_percent,
                database_queries=query_count,
                cache_hit_rate=cache_stats.get('hit_rate', 0.0),
                active_connections=db_connections
            )
            
            # Store metrics for trend analysis
            self.metrics_history.append(metrics)
            
            # Keep only last 100 metrics
            if len(self.metrics_history) > 100:
                self.metrics_history = self.metrics_history[-100:]
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting system metrics: {e}")
            return PerformanceMetrics(
                timestamp=datetime.utcnow(),
                response_time=0.0,
                memory_usage=0.0,
                cpu_usage=0.0,
                database_queries=0,
                cache_hit_rate=0.0,
                active_connections=0
            )
    
    def optimize_database_queries(self) -> Dict[str, Any]:
        """Optimize database queries and return optimization results"""
        optimizations = []
        
        try:
            # Analyze slow queries
            slow_queries = self._analyze_slow_queries()
            if slow_queries:
                optimizations.append({
                    'type': 'slow_queries',
                    'count': len(slow_queries),
                    'action': 'Index optimization recommended',
                    'queries': slow_queries[:5]  # Top 5 slow queries
                })
            
            # Check for missing indexes
            missing_indexes = self._check_missing_indexes()
            if missing_indexes:
                optimizations.append({
                    'type': 'missing_indexes',
                    'count': len(missing_indexes),
                    'action': 'Create recommended indexes',
                    'indexes': missing_indexes
                })
            
            # Analyze table statistics
            table_stats = self._analyze_table_statistics()
            optimizations.append({
                'type': 'table_statistics',
                'action': 'Table analysis completed',
                'stats': table_stats
            })
            
            # Connection pool optimization
            connection_stats = self._analyze_connection_pool()
            if connection_stats['recommendations']:
                optimizations.append({
                    'type': 'connection_pool',
                    'action': 'Connection pool optimization',
                    'recommendations': connection_stats['recommendations']
                })
            
            return {
                'optimizations_applied': len(optimizations),
                'optimizations': optimizations,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error optimizing database queries: {e}")
            return {
                'optimizations_applied': 0,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    def implement_caching_strategy(self) -> Dict[str, Any]:
        """Implement and optimize caching strategies"""
        try:
            caching_improvements = []
            
            # Cache frequently accessed data
            self._cache_user_data()
            caching_improvements.append('User data caching implemented')
            
            # Cache website analysis results
            self._cache_analysis_results()
            caching_improvements.append('Analysis results caching implemented')
            
            # Cache matching results
            self._cache_matching_results()
            caching_improvements.append('Matching results caching implemented')
            
            # Implement query result caching
            self._implement_query_caching()
            caching_improvements.append('Query result caching implemented')
            
            # Cache API responses
            self._cache_api_responses()
            caching_improvements.append('API response caching implemented')
            
            return {
                'caching_strategies_implemented': len(caching_improvements),
                'improvements': caching_improvements,
                'cache_statistics': self._get_cache_statistics(),
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error implementing caching strategy: {e}")
            return {
                'caching_strategies_implemented': 0,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    def optimize_memory_usage(self) -> Dict[str, Any]:
        """Optimize memory usage and garbage collection"""
        try:
            import gc
            
            # Force garbage collection
            collected = gc.collect()
            
            # Get memory statistics
            memory_before = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            
            # Clear unused cache entries
            cache_cleared = self._clear_expired_cache()
            
            # Optimize object references
            self._optimize_object_references()
            
            # Get memory statistics after optimization
            memory_after = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            memory_saved = memory_before - memory_after
            
            return {
                'memory_before_mb': round(memory_before, 2),
                'memory_after_mb': round(memory_after, 2),
                'memory_saved_mb': round(memory_saved, 2),
                'objects_collected': collected,
                'cache_entries_cleared': cache_cleared,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error optimizing memory usage: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    def get_optimization_suggestions(self) -> List[OptimizationSuggestion]:
        """Get performance optimization suggestions based on current metrics"""
        suggestions = []
        
        try:
            current_metrics = self.get_system_metrics()
            
            # Response time suggestions
            if current_metrics.response_time > self.optimization_thresholds['response_time_critical']:
                suggestions.append(OptimizationSuggestion(
                    category='response_time',
                    priority='critical',
                    description='Response time is critically high. Consider database optimization and caching.',
                    impact='high',
                    implementation_effort='medium',
                    estimated_improvement=0.4
                ))
            elif current_metrics.response_time > self.optimization_thresholds['response_time_warning']:
                suggestions.append(OptimizationSuggestion(
                    category='response_time',
                    priority='medium',
                    description='Response time is above optimal. Consider query optimization.',
                    impact='medium',
                    implementation_effort='low',
                    estimated_improvement=0.2
                ))
            
            # Memory usage suggestions
            if current_metrics.memory_usage > self.optimization_thresholds['memory_usage_critical']:
                suggestions.append(OptimizationSuggestion(
                    category='memory',
                    priority='critical',
                    description='Memory usage is critically high. Implement memory optimization.',
                    impact='high',
                    implementation_effort='medium',
                    estimated_improvement=0.3
                ))
            
            # CPU usage suggestions
            if current_metrics.cpu_usage > self.optimization_thresholds['cpu_usage_critical']:
                suggestions.append(OptimizationSuggestion(
                    category='cpu',
                    priority='critical',
                    description='CPU usage is critically high. Consider load balancing or optimization.',
                    impact='high',
                    implementation_effort='high',
                    estimated_improvement=0.35
                ))
            
            # Cache hit rate suggestions
            if current_metrics.cache_hit_rate < self.optimization_thresholds['cache_hit_rate_critical']:
                suggestions.append(OptimizationSuggestion(
                    category='caching',
                    priority='high',
                    description='Cache hit rate is low. Improve caching strategy.',
                    impact='high',
                    implementation_effort='medium',
                    estimated_improvement=0.25
                ))
            
            # Database connection suggestions
            if current_metrics.active_connections > 50:
                suggestions.append(OptimizationSuggestion(
                    category='database',
                    priority='medium',
                    description='High number of database connections. Consider connection pooling optimization.',
                    impact='medium',
                    implementation_effort='low',
                    estimated_improvement=0.15
                ))
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error getting optimization suggestions: {e}")
            return []
    
    def _record_performance_metrics(self, metrics: Dict[str, Any]):
        """Record performance metrics to cache"""
        try:
            key = f"performance_metrics:{datetime.utcnow().strftime('%Y%m%d%H%M')}"
            self.redis_client.lpush(key, str(metrics))
            self.redis_client.expire(key, 3600)  # 1 hour
        except Exception as e:
            logger.error(f"Error recording performance metrics: {e}")
    
    def _get_database_connection_count(self) -> int:
        """Get current database connection count"""
        try:
            result = db.session.execute(text("SELECT count(*) FROM pg_stat_activity"))
            return result.scalar() or 0
        except Exception:
            return 0
    
    def _get_cache_statistics(self) -> Dict[str, float]:
        """Get cache statistics"""
        try:
            info = self.redis_client.info()
            hits = info.get('keyspace_hits', 0)
            misses = info.get('keyspace_misses', 0)
            total = hits + misses
            hit_rate = (hits / total * 100) if total > 0 else 0
            
            return {
                'hit_rate': hit_rate,
                'hits': hits,
                'misses': misses,
                'total_keys': info.get('db0', {}).get('keys', 0) if 'db0' in info else 0
            }
        except Exception:
            return {'hit_rate': 0.0, 'hits': 0, 'misses': 0, 'total_keys': 0}
    
    def _get_recent_query_count(self) -> int:
        """Get recent database query count"""
        try:
            # This is a simplified implementation
            # In production, would track actual query counts
            return len(self.metrics_history) * 10  # Estimate
        except Exception:
            return 0
    
    def _get_average_response_time(self) -> float:
        """Get average response time from recent metrics"""
        try:
            if not self.metrics_history:
                return 0.0
            
            recent_metrics = self.metrics_history[-10:]  # Last 10 metrics
            total_time = sum(m.response_time for m in recent_metrics)
            return total_time / len(recent_metrics)
        except Exception:
            return 0.0
    
    def _analyze_slow_queries(self) -> List[Dict[str, Any]]:
        """Analyze slow database queries"""
        # Mock implementation - would analyze actual slow query logs
        return [
            {
                'query': 'SELECT * FROM websites WHERE domain_authority > ?',
                'avg_duration': 2.5,
                'call_count': 150,
                'recommendation': 'Add index on domain_authority'
            },
            {
                'query': 'SELECT * FROM content_analysis WHERE created_at > ?',
                'avg_duration': 1.8,
                'call_count': 200,
                'recommendation': 'Add index on created_at'
            }
        ]
    
    def _check_missing_indexes(self) -> List[Dict[str, str]]:
        """Check for missing database indexes"""
        # Mock implementation - would analyze actual query patterns
        return [
            {
                'table': 'websites',
                'column': 'domain_authority',
                'reason': 'Frequently used in WHERE clauses'
            },
            {
                'table': 'backlinks',
                'column': 'status',
                'reason': 'Frequently filtered by status'
            }
        ]
    
    def _analyze_table_statistics(self) -> Dict[str, Any]:
        """Analyze database table statistics"""
        try:
            stats = {}
            
            # Get table sizes
            tables = ['users', 'websites', 'backlinks', 'content_analysis']
            for table in tables:
                try:
                    result = db.session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    count = result.scalar()
                    stats[table] = {'row_count': count}
                except Exception:
                    stats[table] = {'row_count': 0}
            
            return stats
        except Exception:
            return {}
    
    def _analyze_connection_pool(self) -> Dict[str, Any]:
        """Analyze database connection pool"""
        return {
            'current_connections': self._get_database_connection_count(),
            'max_connections': 100,  # Configuration dependent
            'recommendations': [
                'Consider increasing connection pool size',
                'Implement connection timeout optimization'
            ]
        }
    
    def _cache_user_data(self):
        """Cache frequently accessed user data"""
        # Implementation would cache user profiles, preferences, etc.
        pass
    
    def _cache_analysis_results(self):
        """Cache content analysis results"""
        # Implementation would cache analysis results
        pass
    
    def _cache_matching_results(self):
        """Cache matching algorithm results"""
        # Implementation would cache matching results
        pass
    
    def _implement_query_caching(self):
        """Implement database query result caching"""
        # Implementation would cache query results
        pass
    
    def _cache_api_responses(self):
        """Cache API response data"""
        # Implementation would cache API responses
        pass
    
    def _clear_expired_cache(self) -> int:
        """Clear expired cache entries"""
        try:
            # Get all keys and check expiration
            keys = self.redis_client.keys('*')
            cleared = 0
            
            for key in keys:
                ttl = self.redis_client.ttl(key)
                if ttl == -1:  # No expiration set
                    # Set expiration for old keys
                    self.redis_client.expire(key, 3600)
                elif ttl == -2:  # Key doesn't exist
                    cleared += 1
            
            return cleared
        except Exception:
            return 0
    
    def _optimize_object_references(self):
        """Optimize object references to reduce memory usage"""
        # Implementation would optimize object references
        pass
