"""
CDN Integration Service for LinkUp Plugin
Handles content delivery network integration for static assets and performance optimization
"""
import logging
import os
import hashlib
import mimetypes
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from urllib.parse import urljoin
import boto3
from botocore.exceptions import ClientError, NoCredentialsError

logger = logging.getLogger(__name__)


@dataclass
class CDNAsset:
    """CDN asset information"""
    filename: str
    url: str
    size: int
    content_type: str
    etag: str
    last_modified: datetime
    cache_control: str


@dataclass
class CDNStats:
    """CDN usage statistics"""
    total_assets: int
    total_size_bytes: int
    bandwidth_used_bytes: int
    requests_count: int
    cache_hit_rate: float
    cost_estimate: float


class CDNService:
    """Service for CDN integration and asset management"""
    
    def __init__(self):
        self.aws_access_key = os.getenv('AWS_ACCESS_KEY_ID')
        self.aws_secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
        self.aws_region = os.getenv('AWS_REGION', 'us-east-1')
        self.s3_bucket = os.getenv('CDN_S3_BUCKET', 'linkup-cdn-assets')
        self.cloudfront_domain = os.getenv('CLOUDFRONT_DOMAIN', 'cdn.linkup.com')
        
        # Initialize AWS clients
        try:
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=self.aws_access_key,
                aws_secret_access_key=self.aws_secret_key,
                region_name=self.aws_region
            )
            self.cloudfront_client = boto3.client(
                'cloudfront',
                aws_access_key_id=self.aws_access_key,
                aws_secret_access_key=self.aws_secret_key,
                region_name=self.aws_region
            )
            self.cdn_enabled = True
        except (NoCredentialsError, Exception) as e:
            logger.warning(f"CDN not configured: {e}")
            self.s3_client = None
            self.cloudfront_client = None
            self.cdn_enabled = False
        
        # Asset configuration
        self.asset_types = {
            'images': {
                'extensions': ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'],
                'cache_control': 'public, max-age=31536000',  # 1 year
                'compress': True
            },
            'stylesheets': {
                'extensions': ['.css'],
                'cache_control': 'public, max-age=86400',  # 1 day
                'compress': True
            },
            'scripts': {
                'extensions': ['.js'],
                'cache_control': 'public, max-age=86400',  # 1 day
                'compress': True
            },
            'fonts': {
                'extensions': ['.woff', '.woff2', '.ttf', '.eot'],
                'cache_control': 'public, max-age=31536000',  # 1 year
                'compress': False
            },
            'documents': {
                'extensions': ['.pdf', '.doc', '.docx'],
                'cache_control': 'public, max-age=3600',  # 1 hour
                'compress': False
            }
        }
    
    def upload_asset(self, file_path: str, asset_key: str = None, 
                    asset_type: str = None) -> Optional[CDNAsset]:
        """Upload asset to CDN"""
        if not self.cdn_enabled:
            logger.warning("CDN not enabled, skipping upload")
            return None
        
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            
            # Generate asset key if not provided
            if not asset_key:
                filename = os.path.basename(file_path)
                file_hash = self._calculate_file_hash(file_path)
                asset_key = f"assets/{file_hash[:8]}/{filename}"
            
            # Determine asset type and configuration
            if not asset_type:
                asset_type = self._determine_asset_type(file_path)
            
            config = self.asset_types.get(asset_type, {})
            
            # Get file information
            file_size = os.path.getsize(file_path)
            content_type = mimetypes.guess_type(file_path)[0] or 'application/octet-stream'
            
            # Upload to S3
            extra_args = {
                'ContentType': content_type,
                'CacheControl': config.get('cache_control', 'public, max-age=3600')
            }
            
            if config.get('compress', False):
                extra_args['ContentEncoding'] = 'gzip'
            
            with open(file_path, 'rb') as file:
                self.s3_client.upload_fileobj(
                    file,
                    self.s3_bucket,
                    asset_key,
                    ExtraArgs=extra_args
                )
            
            # Get uploaded object information
            response = self.s3_client.head_object(Bucket=self.s3_bucket, Key=asset_key)
            
            # Create CDN asset object
            cdn_asset = CDNAsset(
                filename=os.path.basename(file_path),
                url=f"https://{self.cloudfront_domain}/{asset_key}",
                size=file_size,
                content_type=content_type,
                etag=response['ETag'].strip('"'),
                last_modified=response['LastModified'],
                cache_control=extra_args['CacheControl']
            )
            
            logger.info(f"Uploaded asset to CDN: {asset_key}")
            return cdn_asset
            
        except Exception as e:
            logger.error(f"Failed to upload asset to CDN: {e}")
            return None
    
    def delete_asset(self, asset_key: str) -> bool:
        """Delete asset from CDN"""
        if not self.cdn_enabled:
            return False
        
        try:
            self.s3_client.delete_object(Bucket=self.s3_bucket, Key=asset_key)
            
            # Invalidate CloudFront cache
            self._invalidate_cache([asset_key])
            
            logger.info(f"Deleted asset from CDN: {asset_key}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete asset from CDN: {e}")
            return False
    
    def get_asset_url(self, asset_key: str, expires_in: int = None) -> str:
        """Get CDN URL for asset"""
        if not self.cdn_enabled:
            return f"/static/{asset_key}"  # Fallback to local static files
        
        base_url = f"https://{self.cloudfront_domain}/{asset_key}"
        
        if expires_in:
            # Generate signed URL for private assets
            try:
                response = self.s3_client.generate_presigned_url(
                    'get_object',
                    Params={'Bucket': self.s3_bucket, 'Key': asset_key},
                    ExpiresIn=expires_in
                )
                return response
            except Exception as e:
                logger.error(f"Failed to generate signed URL: {e}")
                return base_url
        
        return base_url
    
    def list_assets(self, prefix: str = None, limit: int = 100) -> List[CDNAsset]:
        """List assets in CDN"""
        if not self.cdn_enabled:
            return []
        
        try:
            kwargs = {'Bucket': self.s3_bucket, 'MaxKeys': limit}
            if prefix:
                kwargs['Prefix'] = prefix
            
            response = self.s3_client.list_objects_v2(**kwargs)
            assets = []
            
            for obj in response.get('Contents', []):
                # Get additional object metadata
                head_response = self.s3_client.head_object(
                    Bucket=self.s3_bucket, 
                    Key=obj['Key']
                )
                
                asset = CDNAsset(
                    filename=os.path.basename(obj['Key']),
                    url=f"https://{self.cloudfront_domain}/{obj['Key']}",
                    size=obj['Size'],
                    content_type=head_response.get('ContentType', 'application/octet-stream'),
                    etag=obj['ETag'].strip('"'),
                    last_modified=obj['LastModified'],
                    cache_control=head_response.get('CacheControl', '')
                )
                assets.append(asset)
            
            return assets
            
        except Exception as e:
            logger.error(f"Failed to list CDN assets: {e}")
            return []
    
    def get_cdn_stats(self) -> CDNStats:
        """Get CDN usage statistics"""
        if not self.cdn_enabled:
            return CDNStats(0, 0, 0, 0, 0.0, 0.0)
        
        try:
            # Get S3 bucket statistics
            total_assets = 0
            total_size = 0
            
            paginator = self.s3_client.get_paginator('list_objects_v2')
            for page in paginator.paginate(Bucket=self.s3_bucket):
                for obj in page.get('Contents', []):
                    total_assets += 1
                    total_size += obj['Size']
            
            # Get CloudFront statistics (simplified)
            # In production, would use CloudWatch metrics
            bandwidth_used = total_size * 10  # Estimate
            requests_count = total_assets * 100  # Estimate
            cache_hit_rate = 85.0  # Estimate
            cost_estimate = (bandwidth_used / 1024 / 1024 / 1024) * 0.085  # $0.085 per GB
            
            return CDNStats(
                total_assets=total_assets,
                total_size_bytes=total_size,
                bandwidth_used_bytes=bandwidth_used,
                requests_count=requests_count,
                cache_hit_rate=cache_hit_rate,
                cost_estimate=cost_estimate
            )
            
        except Exception as e:
            logger.error(f"Failed to get CDN stats: {e}")
            return CDNStats(0, 0, 0, 0, 0.0, 0.0)
    
    def optimize_assets(self, asset_keys: List[str] = None) -> Dict[str, Any]:
        """Optimize CDN assets (compression, format conversion, etc.)"""
        if not self.cdn_enabled:
            return {'optimized': 0, 'error': 'CDN not enabled'}
        
        try:
            optimized_count = 0
            optimizations = []
            
            # Get assets to optimize
            if not asset_keys:
                assets = self.list_assets()
                asset_keys = [asset.filename for asset in assets]
            
            for asset_key in asset_keys:
                try:
                    # Check if asset needs optimization
                    optimization_result = self._optimize_single_asset(asset_key)
                    if optimization_result:
                        optimized_count += 1
                        optimizations.append(optimization_result)
                        
                except Exception as e:
                    logger.error(f"Failed to optimize asset {asset_key}: {e}")
            
            return {
                'optimized': optimized_count,
                'optimizations': optimizations,
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to optimize assets: {e}")
            return {'optimized': 0, 'error': str(e)}
    
    def invalidate_cache(self, asset_keys: List[str] = None) -> bool:
        """Invalidate CloudFront cache for specified assets"""
        if not self.cdn_enabled:
            return False
        
        try:
            if not asset_keys:
                # Invalidate all
                paths = ['/*']
            else:
                paths = [f"/{key}" for key in asset_keys]
            
            return self._invalidate_cache(paths)
            
        except Exception as e:
            logger.error(f"Failed to invalidate cache: {e}")
            return False
    
    def setup_cdn_distribution(self) -> Dict[str, Any]:
        """Set up CloudFront distribution for the application"""
        if not self.cdn_enabled:
            return {'success': False, 'error': 'CDN not enabled'}
        
        try:
            # Check if distribution already exists
            distributions = self.cloudfront_client.list_distributions()
            
            for dist in distributions.get('DistributionList', {}).get('Items', []):
                if self.s3_bucket in str(dist.get('Origins', {})):
                    return {
                        'success': True,
                        'distribution_id': dist['Id'],
                        'domain_name': dist['DomainName'],
                        'status': dist['Status'],
                        'message': 'Distribution already exists'
                    }
            
            # Create new distribution
            distribution_config = self._create_distribution_config()
            
            response = self.cloudfront_client.create_distribution(
                DistributionConfig=distribution_config
            )
            
            distribution = response['Distribution']
            
            return {
                'success': True,
                'distribution_id': distribution['Id'],
                'domain_name': distribution['DomainName'],
                'status': distribution['Status'],
                'message': 'Distribution created successfully'
            }
            
        except Exception as e:
            logger.error(f"Failed to setup CDN distribution: {e}")
            return {'success': False, 'error': str(e)}
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA-256 hash of file"""
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def _determine_asset_type(self, file_path: str) -> str:
        """Determine asset type based on file extension"""
        _, ext = os.path.splitext(file_path.lower())
        
        for asset_type, config in self.asset_types.items():
            if ext in config['extensions']:
                return asset_type
        
        return 'documents'  # Default
    
    def _optimize_single_asset(self, asset_key: str) -> Optional[Dict[str, Any]]:
        """Optimize a single asset"""
        try:
            # Get asset metadata
            response = self.s3_client.head_object(Bucket=self.s3_bucket, Key=asset_key)
            
            # Check if optimization is needed
            content_type = response.get('ContentType', '')
            size = response.get('ContentLength', 0)
            
            optimization = {
                'asset_key': asset_key,
                'original_size': size,
                'optimizations_applied': []
            }
            
            # Apply optimizations based on content type
            if content_type.startswith('image/'):
                # Image optimization would be implemented here
                optimization['optimizations_applied'].append('image_compression')
                optimization['new_size'] = int(size * 0.8)  # Simulate 20% reduction
            
            elif content_type in ['text/css', 'application/javascript']:
                # CSS/JS minification would be implemented here
                optimization['optimizations_applied'].append('minification')
                optimization['new_size'] = int(size * 0.7)  # Simulate 30% reduction
            
            if optimization['optimizations_applied']:
                return optimization
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to optimize asset {asset_key}: {e}")
            return None
    
    def _invalidate_cache(self, paths: List[str]) -> bool:
        """Invalidate CloudFront cache for specified paths"""
        try:
            # Get distribution ID (simplified - would be stored/configured)
            distributions = self.cloudfront_client.list_distributions()
            distribution_id = None
            
            for dist in distributions.get('DistributionList', {}).get('Items', []):
                if self.s3_bucket in str(dist.get('Origins', {})):
                    distribution_id = dist['Id']
                    break
            
            if not distribution_id:
                logger.warning("No CloudFront distribution found")
                return False
            
            # Create invalidation
            response = self.cloudfront_client.create_invalidation(
                DistributionId=distribution_id,
                InvalidationBatch={
                    'Paths': {
                        'Quantity': len(paths),
                        'Items': paths
                    },
                    'CallerReference': str(datetime.utcnow().timestamp())
                }
            )
            
            logger.info(f"Created cache invalidation: {response['Invalidation']['Id']}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to invalidate cache: {e}")
            return False
    
    def _create_distribution_config(self) -> Dict[str, Any]:
        """Create CloudFront distribution configuration"""
        return {
            'CallerReference': str(datetime.utcnow().timestamp()),
            'Comment': 'LinkUp Plugin CDN Distribution',
            'DefaultCacheBehavior': {
                'TargetOriginId': f'{self.s3_bucket}-origin',
                'ViewerProtocolPolicy': 'redirect-to-https',
                'TrustedSigners': {
                    'Enabled': False,
                    'Quantity': 0
                },
                'ForwardedValues': {
                    'QueryString': False,
                    'Cookies': {'Forward': 'none'}
                },
                'MinTTL': 0,
                'DefaultTTL': 86400,
                'MaxTTL': 31536000
            },
            'Origins': {
                'Quantity': 1,
                'Items': [
                    {
                        'Id': f'{self.s3_bucket}-origin',
                        'DomainName': f'{self.s3_bucket}.s3.amazonaws.com',
                        'S3OriginConfig': {
                            'OriginAccessIdentity': ''
                        }
                    }
                ]
            },
            'Enabled': True,
            'PriceClass': 'PriceClass_100'  # Use only US, Canada, and Europe
        }
