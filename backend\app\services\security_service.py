"""
Security Service for LinkUp Plugin
Implements enterprise-grade security including encryption, GDPR compliance, and security auditing
"""
import logging
import hashlib
import secrets
import base64
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
from enum import Enum
try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False
    logger.warning("Cryptography library not available, using fallback encryption")

try:
    import bcrypt
    BCRYPT_AVAILABLE = True
except ImportError:
    BCRYPT_AVAILABLE = False
    logger.warning("bcrypt library not available, using fallback password hashing")

import re

from app import db
from app.models.user import User

logger = logging.getLogger(__name__)


class SecurityLevel(Enum):
    """Security level classifications"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class DataClassification(Enum):
    """Data classification levels"""
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"


@dataclass
class SecurityAuditEvent:
    """Security audit event"""
    event_id: str
    event_type: str
    user_id: Optional[int]
    ip_address: str
    user_agent: str
    timestamp: datetime
    severity: SecurityLevel
    description: str
    metadata: Dict[str, Any]


@dataclass
class EncryptionResult:
    """Encryption operation result"""
    success: bool
    encrypted_data: Optional[str] = None
    key_id: Optional[str] = None
    error_message: Optional[str] = None


@dataclass
class SecurityAssessment:
    """Security assessment result"""
    overall_score: float
    vulnerabilities: List[Dict[str, Any]]
    recommendations: List[Dict[str, Any]]
    compliance_status: Dict[str, bool]
    last_assessment: datetime


class SecurityService:
    """Service for security hardening and compliance"""
    
    def __init__(self):
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)
        self.audit_events = []
        
        # Security policies
        self.password_policy = {
            'min_length': 12,
            'require_uppercase': True,
            'require_lowercase': True,
            'require_numbers': True,
            'require_special_chars': True,
            'max_age_days': 90,
            'history_count': 5
        }
        
        # GDPR compliance settings
        self.gdpr_settings = {
            'data_retention_days': 365 * 3,  # 3 years
            'consent_required': True,
            'right_to_be_forgotten': True,
            'data_portability': True,
            'breach_notification_hours': 72
        }
        
        # Security thresholds
        self.security_thresholds = {
            'failed_login_attempts': 5,
            'session_timeout_minutes': 30,
            'api_rate_limit_per_hour': 1000,
            'suspicious_activity_threshold': 10
        }
    
    def encrypt_sensitive_data(self, data: str, classification: DataClassification = DataClassification.CONFIDENTIAL) -> EncryptionResult:
        """Encrypt sensitive data based on classification level"""
        try:
            if not data:
                return EncryptionResult(success=False, error_message="No data provided")
            
            # Convert data to bytes
            data_bytes = data.encode('utf-8')
            
            # Apply encryption based on classification
            if classification in [DataClassification.CONFIDENTIAL, DataClassification.RESTRICTED]:
                # Use strong encryption for sensitive data
                encrypted_bytes = self.cipher_suite.encrypt(data_bytes)
                encrypted_data = base64.b64encode(encrypted_bytes).decode('utf-8')
            else:
                # Use basic encoding for less sensitive data
                encrypted_data = base64.b64encode(data_bytes).decode('utf-8')
            
            # Generate key ID for tracking
            key_id = hashlib.sha256(self.encryption_key).hexdigest()[:16]
            
            # Log encryption event
            self._log_security_event(
                event_type='data_encryption',
                description=f'Data encrypted with classification: {classification.value}',
                severity=SecurityLevel.LOW,
                metadata={'classification': classification.value, 'key_id': key_id}
            )
            
            return EncryptionResult(
                success=True,
                encrypted_data=encrypted_data,
                key_id=key_id
            )
            
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            return EncryptionResult(success=False, error_message=str(e))
    
    def decrypt_sensitive_data(self, encrypted_data: str, key_id: str = None) -> Tuple[bool, Optional[str]]:
        """Decrypt sensitive data"""
        try:
            if not encrypted_data:
                return False, None
            
            # Decode base64
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            
            # Attempt decryption
            try:
                decrypted_bytes = self.cipher_suite.decrypt(encrypted_bytes)
                decrypted_data = decrypted_bytes.decode('utf-8')
            except Exception:
                # Fallback to basic decoding for less sensitive data
                decrypted_data = base64.b64decode(encrypted_data).decode('utf-8')
            
            # Log decryption event
            self._log_security_event(
                event_type='data_decryption',
                description='Data decrypted successfully',
                severity=SecurityLevel.LOW,
                metadata={'key_id': key_id}
            )
            
            return True, decrypted_data
            
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            self._log_security_event(
                event_type='decryption_failure',
                description=f'Decryption failed: {str(e)}',
                severity=SecurityLevel.MEDIUM,
                metadata={'error': str(e)}
            )
            return False, None
    
    def validate_password_strength(self, password: str) -> Dict[str, Any]:
        """Validate password against security policy"""
        validation_result = {
            'is_valid': True,
            'score': 0,
            'issues': [],
            'suggestions': []
        }
        
        # Check minimum length
        if len(password) < self.password_policy['min_length']:
            validation_result['is_valid'] = False
            validation_result['issues'].append(f"Password must be at least {self.password_policy['min_length']} characters")
        else:
            validation_result['score'] += 20
        
        # Check uppercase requirement
        if self.password_policy['require_uppercase'] and not re.search(r'[A-Z]', password):
            validation_result['is_valid'] = False
            validation_result['issues'].append("Password must contain at least one uppercase letter")
        else:
            validation_result['score'] += 15
        
        # Check lowercase requirement
        if self.password_policy['require_lowercase'] and not re.search(r'[a-z]', password):
            validation_result['is_valid'] = False
            validation_result['issues'].append("Password must contain at least one lowercase letter")
        else:
            validation_result['score'] += 15
        
        # Check numbers requirement
        if self.password_policy['require_numbers'] and not re.search(r'\d', password):
            validation_result['is_valid'] = False
            validation_result['issues'].append("Password must contain at least one number")
        else:
            validation_result['score'] += 20
        
        # Check special characters requirement
        if self.password_policy['require_special_chars'] and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            validation_result['is_valid'] = False
            validation_result['issues'].append("Password must contain at least one special character")
        else:
            validation_result['score'] += 20
        
        # Check for common patterns
        common_patterns = ['123456', 'password', 'qwerty', 'abc123']
        if any(pattern in password.lower() for pattern in common_patterns):
            validation_result['score'] -= 30
            validation_result['suggestions'].append("Avoid common password patterns")
        
        # Check for repeated characters
        if re.search(r'(.)\1{2,}', password):
            validation_result['score'] -= 10
            validation_result['suggestions'].append("Avoid repeating characters")
        
        # Ensure score is within bounds
        validation_result['score'] = max(0, min(100, validation_result['score']))
        
        return validation_result
    
    def hash_password_secure(self, password: str) -> str:
        """Create secure password hash using bcrypt"""
        try:
            # Generate salt and hash password
            salt = bcrypt.gensalt(rounds=12)  # High cost factor for security
            password_hash = bcrypt.hashpw(password.encode('utf-8'), salt)
            
            return password_hash.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Password hashing failed: {e}")
            raise Exception("Password hashing failed")
    
    def verify_password_secure(self, password: str, password_hash: str) -> bool:
        """Verify password against secure hash"""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
        except Exception as e:
            logger.error(f"Password verification failed: {e}")
            return False
    
    def implement_gdpr_compliance(self, user_id: int) -> Dict[str, Any]:
        """Implement GDPR compliance measures for user"""
        try:
            user = User.query.get(user_id)
            if not user:
                return {'success': False, 'error': 'User not found'}
            
            compliance_actions = []
            
            # Data consent tracking
            if not user.data_collection_consent:
                compliance_actions.append({
                    'action': 'consent_required',
                    'description': 'User must provide explicit consent for data collection',
                    'priority': 'high'
                })
            
            # Data retention check
            account_age = (datetime.utcnow() - user.created_at).days
            if account_age > self.gdpr_settings['data_retention_days']:
                compliance_actions.append({
                    'action': 'data_review_required',
                    'description': 'Account data exceeds retention period',
                    'priority': 'medium'
                })
            
            # Privacy settings validation
            if not user.privacy_settings:
                compliance_actions.append({
                    'action': 'privacy_settings_required',
                    'description': 'User must configure privacy settings',
                    'priority': 'medium'
                })
            
            # Marketing consent check
            if not user.marketing_consent:
                compliance_actions.append({
                    'action': 'marketing_consent_missing',
                    'description': 'Marketing communications require explicit consent',
                    'priority': 'low'
                })
            
            return {
                'success': True,
                'user_id': user_id,
                'compliance_status': len(compliance_actions) == 0,
                'actions_required': compliance_actions,
                'data_subject_rights': {
                    'right_to_access': True,
                    'right_to_rectification': True,
                    'right_to_erasure': True,
                    'right_to_portability': True,
                    'right_to_object': True
                }
            }
            
        except Exception as e:
            logger.error(f"GDPR compliance check failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def export_user_data(self, user_id: int) -> Dict[str, Any]:
        """Export user data for GDPR compliance (data portability)"""
        try:
            user = User.query.get(user_id)
            if not user:
                return {'success': False, 'error': 'User not found'}
            
            # Collect user data
            user_data = {
                'personal_information': {
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'company': user.company,
                    'created_at': user.created_at.isoformat() if user.created_at else None,
                    'last_login_at': user.last_login_at.isoformat() if user.last_login_at else None
                },
                'account_settings': {
                    'plan': user.plan,
                    'is_active': user.is_active,
                    'is_verified': user.is_verified,
                    'two_factor_enabled': user.two_factor_enabled
                },
                'privacy_preferences': {
                    'data_collection_consent': user.data_collection_consent,
                    'analytics_consent': user.analytics_consent,
                    'marketing_consent': user.marketing_consent,
                    'privacy_settings': user.privacy_settings
                },
                'websites': [],
                'usage_statistics': user.get_usage_this_month()
            }
            
            # Add website data
            for website in user.websites:
                user_data['websites'].append({
                    'url': website.url,
                    'title': website.title,
                    'description': website.description,
                    'created_at': website.created_at.isoformat() if website.created_at else None
                })
            
            # Log data export
            self._log_security_event(
                event_type='data_export',
                user_id=user_id,
                description='User data exported for GDPR compliance',
                severity=SecurityLevel.LOW,
                metadata={'export_type': 'gdpr_portability'}
            )
            
            return {
                'success': True,
                'user_id': user_id,
                'export_date': datetime.utcnow().isoformat(),
                'data': user_data
            }
            
        except Exception as e:
            logger.error(f"Data export failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def delete_user_data(self, user_id: int, verification_token: str) -> Dict[str, Any]:
        """Delete user data for GDPR compliance (right to be forgotten)"""
        try:
            user = User.query.get(user_id)
            if not user:
                return {'success': False, 'error': 'User not found'}
            
            # Verify deletion token (in production, would implement proper verification)
            expected_token = hashlib.sha256(f"{user_id}:{user.email}".encode()).hexdigest()[:16]
            if verification_token != expected_token:
                return {'success': False, 'error': 'Invalid verification token'}
            
            # Log deletion request
            self._log_security_event(
                event_type='data_deletion_request',
                user_id=user_id,
                description='User requested data deletion (right to be forgotten)',
                severity=SecurityLevel.MEDIUM,
                metadata={'verification_token': verification_token}
            )
            
            # In production, would implement proper data deletion
            # For now, just mark as deleted and anonymize
            user.email = f"deleted_{user_id}@example.com"
            user.first_name = None
            user.last_name = None
            user.company = None
            user.is_active = False
            
            db.session.commit()
            
            return {
                'success': True,
                'user_id': user_id,
                'deletion_date': datetime.utcnow().isoformat(),
                'message': 'User data has been anonymized and marked for deletion'
            }
            
        except Exception as e:
            logger.error(f"Data deletion failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def perform_security_audit(self) -> SecurityAssessment:
        """Perform comprehensive security audit"""
        try:
            vulnerabilities = []
            recommendations = []
            compliance_status = {}
            
            # Check password policies
            weak_passwords = self._audit_password_strength()
            if weak_passwords > 0:
                vulnerabilities.append({
                    'type': 'weak_passwords',
                    'severity': 'medium',
                    'count': weak_passwords,
                    'description': f'{weak_passwords} users have weak passwords'
                })
                recommendations.append({
                    'category': 'authentication',
                    'priority': 'high',
                    'description': 'Enforce stronger password policies',
                    'implementation': 'Update password validation rules'
                })
            
            # Check encryption usage
            unencrypted_data = self._audit_data_encryption()
            if unencrypted_data > 0:
                vulnerabilities.append({
                    'type': 'unencrypted_data',
                    'severity': 'high',
                    'count': unencrypted_data,
                    'description': f'{unencrypted_data} sensitive data fields are unencrypted'
                })
            
            # Check GDPR compliance
            gdpr_issues = self._audit_gdpr_compliance()
            compliance_status['gdpr'] = len(gdpr_issues) == 0
            if gdpr_issues:
                vulnerabilities.extend(gdpr_issues)
            
            # Check session security
            session_issues = self._audit_session_security()
            if session_issues:
                vulnerabilities.extend(session_issues)
            
            # Check API security
            api_issues = self._audit_api_security()
            if api_issues:
                vulnerabilities.extend(api_issues)
            
            # Calculate overall security score
            total_issues = len(vulnerabilities)
            critical_issues = len([v for v in vulnerabilities if v['severity'] == 'critical'])
            high_issues = len([v for v in vulnerabilities if v['severity'] == 'high'])
            medium_issues = len([v for v in vulnerabilities if v['severity'] == 'medium'])
            
            # Scoring algorithm
            score = 100
            score -= critical_issues * 25
            score -= high_issues * 15
            score -= medium_issues * 10
            score = max(0, score)
            
            return SecurityAssessment(
                overall_score=score,
                vulnerabilities=vulnerabilities,
                recommendations=recommendations,
                compliance_status=compliance_status,
                last_assessment=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"Security audit failed: {e}")
            return SecurityAssessment(
                overall_score=0,
                vulnerabilities=[{
                    'type': 'audit_failure',
                    'severity': 'critical',
                    'description': f'Security audit failed: {str(e)}'
                }],
                recommendations=[],
                compliance_status={},
                last_assessment=datetime.utcnow()
            )
    
    def _get_or_create_encryption_key(self) -> bytes:
        """Get or create encryption key"""
        try:
            # In production, would use proper key management
            key_material = "linkup_encryption_key_2024"
            salt = b"linkup_salt_2024"
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(key_material.encode()))
            return key
            
        except Exception as e:
            logger.error(f"Key generation failed: {e}")
            # Fallback to generated key
            return Fernet.generate_key()
    
    def _log_security_event(self, event_type: str, description: str, 
                           severity: SecurityLevel, user_id: int = None, 
                           ip_address: str = None, user_agent: str = None,
                           metadata: Dict[str, Any] = None):
        """Log security audit event"""
        try:
            event = SecurityAuditEvent(
                event_id=secrets.token_hex(16),
                event_type=event_type,
                user_id=user_id,
                ip_address=ip_address or 'unknown',
                user_agent=user_agent or 'unknown',
                timestamp=datetime.utcnow(),
                severity=severity,
                description=description,
                metadata=metadata or {}
            )
            
            self.audit_events.append(event)
            
            # In production, would persist to secure audit log
            logger.info(f"Security event logged: {event_type} - {description}")
            
        except Exception as e:
            logger.error(f"Failed to log security event: {e}")
    
    def _audit_password_strength(self) -> int:
        """Audit password strength across all users"""
        # Mock implementation - would check actual password hashes
        return 5  # Simulate 5 users with weak passwords
    
    def _audit_data_encryption(self) -> int:
        """Audit data encryption usage"""
        # Mock implementation - would check actual data encryption
        return 2  # Simulate 2 unencrypted sensitive fields
    
    def _audit_gdpr_compliance(self) -> List[Dict[str, Any]]:
        """Audit GDPR compliance"""
        # Mock implementation - would check actual compliance
        return [
            {
                'type': 'missing_consent',
                'severity': 'medium',
                'description': 'Some users missing data collection consent'
            }
        ]
    
    def _audit_session_security(self) -> List[Dict[str, Any]]:
        """Audit session security"""
        # Mock implementation - would check actual session configuration
        return []
    
    def _audit_api_security(self) -> List[Dict[str, Any]]:
        """Audit API security"""
        # Mock implementation - would check actual API security
        return []
