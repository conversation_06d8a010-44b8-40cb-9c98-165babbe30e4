#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API threshold {
  using schema = at::Tensor (const at::Tensor &, const at::Scalar &, const at::Scalar &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::threshold";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "threshold(Tensor self, Scalar threshold, Scalar value) -> Tensor";
  static at::Tensor call(const at::Tensor & self, const at::Scalar & threshold, const at::Scalar & value);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Scalar & threshold, const at::Scalar & value);
};

struct TORCH_API threshold_ {
  using schema = at::Tensor & (at::Tensor &, const at::Scalar &, const at::Scalar &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::threshold_";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "threshold_(Tensor(a!) self, Scalar threshold, Scalar value) -> Tensor(a!)";
  static at::Tensor & call(at::Tensor & self, const at::Scalar & threshold, const at::Scalar & value);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, at::Tensor & self, const at::Scalar & threshold, const at::Scalar & value);
};

struct TORCH_API threshold_out {
  using schema = at::Tensor & (const at::Tensor &, const at::Scalar &, const at::Scalar &, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::threshold";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "threshold.out(Tensor self, Scalar threshold, Scalar value, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, const at::Scalar & threshold, const at::Scalar & value, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Scalar & threshold, const at::Scalar & value, at::Tensor & out);
};

}} // namespace at::_ops
