FLASK_APP=app
FLASK_ENV=development
FLASK_DEBUG=1

# Database configuration - replace with your actual PostgreSQL password
DATABASE_URL=postgresql://postgres@localhost:5432/linkup_dev

# Security keys (change these in production)
SECRET_KEY=development-secret-key-123
JWT_SECRET_KEY=development-jwt-key-123

# Redis configuration
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# API Configuration
API_VERSION=v1
API_PREFIX=/api/v1
RATE_LIMIT=200/hour

# Feature flags
DEBUG_MODE=True
MOCK_EXTERNAL_SERVICES=True
