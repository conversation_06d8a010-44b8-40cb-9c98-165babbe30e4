"""
Comprehensive Unit Tests for Sprint 14: Performance Optimization
Tests database optimization, caching strategies, CDN integration, and performance monitoring
"""
import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os
from datetime import datetime, timedelta
import tempfile

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from tests.framework.base_test import BaseTestCase, ServiceTestCase
from app.services.performance_optimization_service import (
    PerformanceOptimizationService, PerformanceMetrics, OptimizationSuggestion
)
from app.services.cdn_service import CDNService, CDNAsset, CDNStats


class TestPerformanceOptimizationService(ServiceTestCase):
    """Test cases for Performance Optimization Service"""
    
    @property
    def service_class(self):
        return PerformanceOptimizationService
    
    def setUp(self):
        super().setUp()
        # Mock Redis client
        self.service.redis_client = Mock()
    
    @patch('psutil.cpu_percent')
    @patch('psutil.virtual_memory')
    def test_get_system_metrics(self, mock_memory, mock_cpu):
        """Test getting system performance metrics"""
        # Mock system metrics
        mock_cpu.return_value = 45.5
        mock_memory.return_value = Mock(percent=65.2)
        
        # Mock other metrics
        with patch.object(self.service, '_get_database_connection_count', return_value=15):
            with patch.object(self.service, '_get_cache_statistics', return_value={'hit_rate': 85.0}):
                with patch.object(self.service, '_get_recent_query_count', return_value=250):
                    with patch.object(self.service, '_get_average_response_time', return_value=0.8):
                        
                        metrics = self.service.get_system_metrics()
                        
                        # Verify metrics structure
                        self.assertIsInstance(metrics, PerformanceMetrics)
                        self.assertEqual(metrics.cpu_usage, 45.5)
                        self.assertEqual(metrics.memory_usage, 65.2)
                        self.assertEqual(metrics.active_connections, 15)
                        self.assertEqual(metrics.cache_hit_rate, 85.0)
                        self.assertEqual(metrics.database_queries, 250)
                        self.assertEqual(metrics.response_time, 0.8)
                        self.assertIsInstance(metrics.timestamp, datetime)
    
    def test_monitor_performance_decorator(self):
        """Test performance monitoring decorator"""
        # Create a test function to monitor
        @self.service.monitor_performance
        def test_function(x, y):
            return x + y
        
        # Mock performance recording
        with patch.object(self.service, '_record_performance_metrics') as mock_record:
            result = test_function(2, 3)
            
            # Verify function executed correctly
            self.assertEqual(result, 5)
            
            # Verify performance was recorded
            mock_record.assert_called_once()
            call_args = mock_record.call_args[0][0]
            self.assertIn('function_name', call_args)
            self.assertIn('execution_time', call_args)
            self.assertIn('timestamp', call_args)
            self.assertEqual(call_args['function_name'], 'test_function')
            self.assertTrue(call_args['success'])
    
    def test_monitor_performance_decorator_with_exception(self):
        """Test performance monitoring decorator with exception"""
        # Create a test function that raises an exception
        @self.service.monitor_performance
        def failing_function():
            raise ValueError("Test error")
        
        # Mock performance recording
        with patch.object(self.service, '_record_performance_metrics') as mock_record:
            with self.assertRaises(ValueError):
                failing_function()
            
            # Verify performance was recorded even with exception
            mock_record.assert_called_once()
            call_args = mock_record.call_args[0][0]
            self.assertEqual(call_args['function_name'], 'failing_function')
            self.assertFalse(call_args['success'])
            self.assertIn('error', call_args)
    
    def test_optimize_database_queries(self):
        """Test database query optimization"""
        # Mock optimization methods
        with patch.object(self.service, '_analyze_slow_queries', return_value=[
            {'query': 'SELECT * FROM websites', 'avg_duration': 2.5}
        ]):
            with patch.object(self.service, '_check_missing_indexes', return_value=[
                {'table': 'websites', 'column': 'domain_authority'}
            ]):
                with patch.object(self.service, '_analyze_table_statistics', return_value={
                    'websites': {'row_count': 1000}
                }):
                    with patch.object(self.service, '_analyze_connection_pool', return_value={
                        'recommendations': ['Increase pool size']
                    }):
                        
                        result = self.service.optimize_database_queries()
                        
                        # Verify optimization results
                        self.assertIn('optimizations_applied', result)
                        self.assertIn('optimizations', result)
                        self.assertIn('timestamp', result)
                        self.assertGreater(result['optimizations_applied'], 0)
                        
                        # Verify optimization types
                        optimization_types = [opt['type'] for opt in result['optimizations']]
                        self.assertIn('slow_queries', optimization_types)
                        self.assertIn('missing_indexes', optimization_types)
                        self.assertIn('table_statistics', optimization_types)
    
    def test_implement_caching_strategy(self):
        """Test caching strategy implementation"""
        # Mock caching methods
        with patch.object(self.service, '_cache_user_data'):
            with patch.object(self.service, '_cache_analysis_results'):
                with patch.object(self.service, '_cache_matching_results'):
                    with patch.object(self.service, '_implement_query_caching'):
                        with patch.object(self.service, '_cache_api_responses'):
                            with patch.object(self.service, '_get_cache_statistics', return_value={
                                'hit_rate': 85.0, 'total_keys': 1500
                            }):
                                
                                result = self.service.implement_caching_strategy()
                                
                                # Verify caching implementation
                                self.assertIn('caching_strategies_implemented', result)
                                self.assertIn('improvements', result)
                                self.assertIn('cache_statistics', result)
                                self.assertEqual(result['caching_strategies_implemented'], 5)
                                self.assertEqual(len(result['improvements']), 5)
    
    @patch('gc.collect')
    @patch('psutil.Process')
    def test_optimize_memory_usage(self, mock_process, mock_gc):
        """Test memory usage optimization"""
        # Mock memory info
        mock_memory_info = Mock()
        mock_memory_info.rss = 100 * 1024 * 1024  # 100 MB
        mock_process.return_value.memory_info.return_value = mock_memory_info
        
        # Mock garbage collection
        mock_gc.return_value = 50  # Objects collected
        
        # Mock cache clearing
        with patch.object(self.service, '_clear_expired_cache', return_value=25):
            with patch.object(self.service, '_optimize_object_references'):
                
                result = self.service.optimize_memory_usage()
                
                # Verify memory optimization
                self.assertIn('memory_before_mb', result)
                self.assertIn('memory_after_mb', result)
                self.assertIn('memory_saved_mb', result)
                self.assertIn('objects_collected', result)
                self.assertIn('cache_entries_cleared', result)
                self.assertEqual(result['objects_collected'], 50)
                self.assertEqual(result['cache_entries_cleared'], 25)
    
    def test_get_optimization_suggestions(self):
        """Test getting optimization suggestions"""
        # Mock metrics with various threshold violations
        mock_metrics = PerformanceMetrics(
            timestamp=datetime.utcnow(),
            response_time=3.5,  # Critical
            memory_usage=85.0,  # Warning
            cpu_usage=95.0,  # Critical
            database_queries=500,
            cache_hit_rate=45.0,  # Critical
            active_connections=60  # High
        )
        
        with patch.object(self.service, 'get_system_metrics', return_value=mock_metrics):
            suggestions = self.service.get_optimization_suggestions()
            
            # Verify suggestions structure
            self.assertIsInstance(suggestions, list)
            self.assertGreater(len(suggestions), 0)
            
            # Verify suggestion structure
            for suggestion in suggestions:
                self.assertIsInstance(suggestion, OptimizationSuggestion)
                self.assertIn(suggestion.category, ['response_time', 'memory', 'cpu', 'caching', 'database'])
                self.assertIn(suggestion.priority, ['low', 'medium', 'high', 'critical'])
                self.assertIn(suggestion.impact, ['low', 'medium', 'high'])
                self.assertIn(suggestion.implementation_effort, ['low', 'medium', 'high'])
                self.assertIsInstance(suggestion.estimated_improvement, float)
            
            # Verify critical suggestions are present
            categories = [s.category for s in suggestions]
            self.assertIn('response_time', categories)
            self.assertIn('cpu', categories)
            self.assertIn('caching', categories)
    
    def test_record_performance_metrics(self):
        """Test recording performance metrics"""
        test_metrics = {
            'function_name': 'test_function',
            'execution_time': 1.5,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Test recording
        self.service._record_performance_metrics(test_metrics)
        
        # Verify Redis operations
        self.service.redis_client.lpush.assert_called_once()
        self.service.redis_client.expire.assert_called_once()
    
    def test_get_cache_statistics(self):
        """Test getting cache statistics"""
        # Mock Redis info
        mock_info = {
            'keyspace_hits': 850,
            'keyspace_misses': 150,
            'db0': {'keys': 1000}
        }
        self.service.redis_client.info.return_value = mock_info
        
        stats = self.service._get_cache_statistics()
        
        # Verify statistics
        self.assertEqual(stats['hits'], 850)
        self.assertEqual(stats['misses'], 150)
        self.assertEqual(stats['hit_rate'], 85.0)  # 850/1000 * 100
        self.assertEqual(stats['total_keys'], 1000)


class TestCDNService(ServiceTestCase):
    """Test cases for CDN Service"""
    
    @property
    def service_class(self):
        return CDNService
    
    def setUp(self):
        super().setUp()
        # Mock AWS clients
        self.service.s3_client = Mock()
        self.service.cloudfront_client = Mock()
        self.service.cdn_enabled = True
    
    def test_upload_asset(self):
        """Test uploading asset to CDN"""
        # Create temporary test file
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
            temp_file.write(b'test image data')
            temp_file_path = temp_file.name
        
        try:
            # Mock S3 responses
            self.service.s3_client.head_object.return_value = {
                'ETag': '"abc123"',
                'LastModified': datetime.utcnow(),
                'ContentType': 'image/jpeg'
            }
            
            # Test upload
            result = self.service.upload_asset(temp_file_path, 'test/image.jpg', 'images')
            
            # Verify upload
            self.assertIsInstance(result, CDNAsset)
            self.assertEqual(result.filename, os.path.basename(temp_file_path))
            self.assertTrue(result.url.startswith('https://'))
            self.assertEqual(result.content_type, 'image/jpeg')
            self.assertEqual(result.etag, 'abc123')
            
            # Verify S3 upload was called
            self.service.s3_client.upload_fileobj.assert_called_once()
            
        finally:
            # Clean up
            os.unlink(temp_file_path)
    
    def test_upload_asset_cdn_disabled(self):
        """Test uploading asset when CDN is disabled"""
        self.service.cdn_enabled = False
        
        result = self.service.upload_asset('/fake/path.jpg')
        
        # Should return None when CDN is disabled
        self.assertIsNone(result)
    
    def test_delete_asset(self):
        """Test deleting asset from CDN"""
        # Mock invalidation
        with patch.object(self.service, '_invalidate_cache', return_value=True):
            result = self.service.delete_asset('test/asset.jpg')
            
            # Verify deletion
            self.assertTrue(result)
            self.service.s3_client.delete_object.assert_called_once_with(
                Bucket=self.service.s3_bucket,
                Key='test/asset.jpg'
            )
    
    def test_get_asset_url(self):
        """Test getting asset URL"""
        # Test regular URL
        url = self.service.get_asset_url('test/asset.jpg')
        expected_url = f"https://{self.service.cloudfront_domain}/test/asset.jpg"
        self.assertEqual(url, expected_url)
        
        # Test signed URL
        self.service.s3_client.generate_presigned_url.return_value = 'https://signed-url.com'
        signed_url = self.service.get_asset_url('test/asset.jpg', expires_in=3600)
        self.assertEqual(signed_url, 'https://signed-url.com')
    
    def test_get_asset_url_cdn_disabled(self):
        """Test getting asset URL when CDN is disabled"""
        self.service.cdn_enabled = False
        
        url = self.service.get_asset_url('test/asset.jpg')
        
        # Should return local static URL
        self.assertEqual(url, '/static/test/asset.jpg')
    
    def test_list_assets(self):
        """Test listing CDN assets"""
        # Mock S3 response
        mock_objects = [
            {
                'Key': 'assets/image1.jpg',
                'Size': 1024,
                'LastModified': datetime.utcnow(),
                'ETag': '"abc123"'
            },
            {
                'Key': 'assets/image2.png',
                'Size': 2048,
                'LastModified': datetime.utcnow(),
                'ETag': '"def456"'
            }
        ]
        
        self.service.s3_client.list_objects_v2.return_value = {
            'Contents': mock_objects
        }
        
        # Mock head_object responses
        self.service.s3_client.head_object.return_value = {
            'ContentType': 'image/jpeg',
            'CacheControl': 'public, max-age=31536000'
        }
        
        assets = self.service.list_assets(prefix='assets/', limit=10)
        
        # Verify assets
        self.assertEqual(len(assets), 2)
        self.assertIsInstance(assets[0], CDNAsset)
        self.assertEqual(assets[0].size, 1024)
        self.assertTrue(assets[0].url.startswith('https://'))
    
    def test_get_cdn_stats(self):
        """Test getting CDN statistics"""
        # Mock paginator
        mock_paginator = Mock()
        mock_page = {
            'Contents': [
                {'Size': 1024},
                {'Size': 2048},
                {'Size': 512}
            ]
        }
        mock_paginator.paginate.return_value = [mock_page]
        self.service.s3_client.get_paginator.return_value = mock_paginator
        
        stats = self.service.get_cdn_stats()
        
        # Verify statistics
        self.assertIsInstance(stats, CDNStats)
        self.assertEqual(stats.total_assets, 3)
        self.assertEqual(stats.total_size_bytes, 3584)  # 1024 + 2048 + 512
        self.assertGreater(stats.bandwidth_used_bytes, 0)
        self.assertGreater(stats.requests_count, 0)
        self.assertGreater(stats.cache_hit_rate, 0)
        self.assertGreaterEqual(stats.cost_estimate, 0)
    
    def test_optimize_assets(self):
        """Test asset optimization"""
        # Mock list_assets
        mock_assets = [
            CDNAsset('image1.jpg', 'url1', 1024, 'image/jpeg', 'etag1', datetime.utcnow(), 'cache1'),
            CDNAsset('script.js', 'url2', 2048, 'application/javascript', 'etag2', datetime.utcnow(), 'cache2')
        ]
        
        with patch.object(self.service, 'list_assets', return_value=mock_assets):
            with patch.object(self.service, '_optimize_single_asset') as mock_optimize:
                mock_optimize.side_effect = [
                    {'asset_key': 'image1.jpg', 'optimizations_applied': ['compression']},
                    {'asset_key': 'script.js', 'optimizations_applied': ['minification']}
                ]
                
                result = self.service.optimize_assets()
                
                # Verify optimization
                self.assertEqual(result['optimized'], 2)
                self.assertEqual(len(result['optimizations']), 2)
                self.assertIn('timestamp', result)
    
    def test_invalidate_cache(self):
        """Test cache invalidation"""
        # Mock CloudFront distribution
        self.service.cloudfront_client.list_distributions.return_value = {
            'DistributionList': {
                'Items': [
                    {
                        'Id': 'dist123',
                        'Origins': {'Items': [{'DomainName': f'{self.service.s3_bucket}.s3.amazonaws.com'}]}
                    }
                ]
            }
        }
        
        # Mock invalidation response
        self.service.cloudfront_client.create_invalidation.return_value = {
            'Invalidation': {'Id': 'inv123'}
        }
        
        result = self.service.invalidate_cache(['asset1.jpg', 'asset2.png'])
        
        # Verify invalidation
        self.assertTrue(result)
        self.service.cloudfront_client.create_invalidation.assert_called_once()
    
    def test_determine_asset_type(self):
        """Test asset type determination"""
        # Test different file types
        self.assertEqual(self.service._determine_asset_type('image.jpg'), 'images')
        self.assertEqual(self.service._determine_asset_type('style.css'), 'stylesheets')
        self.assertEqual(self.service._determine_asset_type('script.js'), 'scripts')
        self.assertEqual(self.service._determine_asset_type('font.woff'), 'fonts')
        self.assertEqual(self.service._determine_asset_type('document.pdf'), 'documents')
        self.assertEqual(self.service._determine_asset_type('unknown.xyz'), 'documents')  # Default
    
    def test_calculate_file_hash(self):
        """Test file hash calculation"""
        # Create temporary test file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(b'test content')
            temp_file_path = temp_file.name
        
        try:
            hash1 = self.service._calculate_file_hash(temp_file_path)
            hash2 = self.service._calculate_file_hash(temp_file_path)
            
            # Hash should be consistent
            self.assertEqual(hash1, hash2)
            self.assertEqual(len(hash1), 64)  # SHA-256 hex length
            
        finally:
            os.unlink(temp_file_path)


if __name__ == '__main__':
    unittest.main()
