"""
Comprehensive Unit Tests for Sprint 15: Security Hardening
Tests enterprise-grade security including encryption, GDPR compliance, and security auditing
"""
import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os
from datetime import datetime, timedelta
import hashlib
import base64

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from tests.framework.base_test import BaseTestCase, ServiceTestCase
from app.services.security_service import (
    SecurityService, SecurityLevel, DataClassification, 
    SecurityAuditEvent, EncryptionResult, SecurityAssessment
)


class TestSecurityService(ServiceTestCase):
    """Test cases for Security Service"""
    
    @property
    def service_class(self):
        return SecurityService
    
    def setUp(self):
        super().setUp()
        self.test_user = self.create_test_user()
        # Mock cryptography dependencies if not available
        if not hasattr(self.service, 'cipher_suite'):
            self.service.cipher_suite = Mock()
    
    def test_encrypt_sensitive_data_confidential(self):
        """Test encrypting confidential data"""
        test_data = "sensitive user information"
        
        # Mock cipher suite if needed
        if hasattr(self.service.cipher_suite, 'encrypt'):
            self.service.cipher_suite.encrypt.return_value = b'encrypted_data'
        
        result = self.service.encrypt_sensitive_data(test_data, DataClassification.CONFIDENTIAL)
        
        # Verify encryption result
        self.assertIsInstance(result, EncryptionResult)
        self.assertTrue(result.success)
        self.assertIsNotNone(result.encrypted_data)
        self.assertIsNotNone(result.key_id)
        self.assertIsNone(result.error_message)
    
    def test_encrypt_sensitive_data_public(self):
        """Test encrypting public data (basic encoding)"""
        test_data = "public information"
        
        result = self.service.encrypt_sensitive_data(test_data, DataClassification.PUBLIC)
        
        # Verify encryption result
        self.assertTrue(result.success)
        self.assertIsNotNone(result.encrypted_data)
        
        # Should be base64 encoded
        decoded = base64.b64decode(result.encrypted_data).decode('utf-8')
        self.assertEqual(decoded, test_data)
    
    def test_encrypt_empty_data(self):
        """Test encrypting empty data"""
        result = self.service.encrypt_sensitive_data("")
        
        # Should fail with empty data
        self.assertFalse(result.success)
        self.assertEqual(result.error_message, "No data provided")
    
    def test_decrypt_sensitive_data(self):
        """Test decrypting sensitive data"""
        test_data = "test data for decryption"
        
        # First encrypt the data
        encrypted_result = self.service.encrypt_sensitive_data(test_data, DataClassification.PUBLIC)
        self.assertTrue(encrypted_result.success)
        
        # Then decrypt it
        success, decrypted_data = self.service.decrypt_sensitive_data(
            encrypted_result.encrypted_data, 
            encrypted_result.key_id
        )
        
        # Verify decryption
        self.assertTrue(success)
        self.assertEqual(decrypted_data, test_data)
    
    def test_decrypt_invalid_data(self):
        """Test decrypting invalid data"""
        success, decrypted_data = self.service.decrypt_sensitive_data("invalid_data")
        
        # Should fail gracefully
        self.assertFalse(success)
        self.assertIsNone(decrypted_data)
    
    def test_validate_password_strength_strong(self):
        """Test validating a strong password"""
        strong_password = "MyStr0ng!P@ssw0rd123"
        
        result = self.service.validate_password_strength(strong_password)
        
        # Verify validation result
        self.assertTrue(result['is_valid'])
        self.assertGreater(result['score'], 80)
        self.assertEqual(len(result['issues']), 0)
    
    def test_validate_password_strength_weak(self):
        """Test validating a weak password"""
        weak_password = "123456"
        
        result = self.service.validate_password_strength(weak_password)
        
        # Verify validation result
        self.assertFalse(result['is_valid'])
        self.assertLess(result['score'], 50)
        self.assertGreater(len(result['issues']), 0)
        
        # Check specific issues
        issues = result['issues']
        self.assertTrue(any('at least 12 characters' in issue for issue in issues))
        self.assertTrue(any('uppercase letter' in issue for issue in issues))
        self.assertTrue(any('lowercase letter' in issue for issue in issues))
        self.assertTrue(any('special character' in issue for issue in issues))
    
    def test_validate_password_strength_common_patterns(self):
        """Test validating password with common patterns"""
        common_password = "Password123456"
        
        result = self.service.validate_password_strength(common_password)
        
        # Should detect common patterns
        self.assertIn("Avoid common password patterns", result['suggestions'])
    
    def test_validate_password_strength_repeated_characters(self):
        """Test validating password with repeated characters"""
        repeated_password = "MyPasssssword123!"
        
        result = self.service.validate_password_strength(repeated_password)
        
        # Should detect repeated characters
        self.assertIn("Avoid repeating characters", result['suggestions'])
    
    @patch('app.services.security_service.BCRYPT_AVAILABLE', True)
    def test_hash_password_secure(self):
        """Test secure password hashing"""
        password = "test_password_123"
        
        # Mock bcrypt if not available
        with patch('app.services.security_service.bcrypt') as mock_bcrypt:
            mock_bcrypt.gensalt.return_value = b'salt'
            mock_bcrypt.hashpw.return_value = b'hashed_password'
            
            password_hash = self.service.hash_password_secure(password)
            
            # Verify hashing
            self.assertEqual(password_hash, 'hashed_password')
            mock_bcrypt.gensalt.assert_called_once_with(rounds=12)
            mock_bcrypt.hashpw.assert_called_once()
    
    @patch('app.services.security_service.BCRYPT_AVAILABLE', True)
    def test_verify_password_secure(self):
        """Test secure password verification"""
        password = "test_password_123"
        password_hash = "hashed_password"
        
        # Mock bcrypt if not available
        with patch('app.services.security_service.bcrypt') as mock_bcrypt:
            mock_bcrypt.checkpw.return_value = True
            
            result = self.service.verify_password_secure(password, password_hash)
            
            # Verify verification
            self.assertTrue(result)
            mock_bcrypt.checkpw.assert_called_once()
    
    def test_implement_gdpr_compliance_compliant_user(self):
        """Test GDPR compliance for compliant user"""
        # Set up compliant user
        self.test_user.data_collection_consent = True
        self.test_user.marketing_consent = True
        self.test_user.privacy_settings = {'notifications': True}
        
        result = self.service.implement_gdpr_compliance(self.test_user.id)
        
        # Verify compliance result
        self.assertTrue(result['success'])
        self.assertTrue(result['compliance_status'])
        self.assertEqual(len(result['actions_required']), 0)
        
        # Verify data subject rights
        rights = result['data_subject_rights']
        self.assertTrue(rights['right_to_access'])
        self.assertTrue(rights['right_to_rectification'])
        self.assertTrue(rights['right_to_erasure'])
        self.assertTrue(rights['right_to_portability'])
        self.assertTrue(rights['right_to_object'])
    
    def test_implement_gdpr_compliance_non_compliant_user(self):
        """Test GDPR compliance for non-compliant user"""
        # Set up non-compliant user
        self.test_user.data_collection_consent = False
        self.test_user.marketing_consent = False
        self.test_user.privacy_settings = None
        
        result = self.service.implement_gdpr_compliance(self.test_user.id)
        
        # Verify compliance result
        self.assertTrue(result['success'])
        self.assertFalse(result['compliance_status'])
        self.assertGreater(len(result['actions_required']), 0)
        
        # Check specific actions
        actions = [action['action'] for action in result['actions_required']]
        self.assertIn('consent_required', actions)
        self.assertIn('privacy_settings_required', actions)
        self.assertIn('marketing_consent_missing', actions)
    
    def test_export_user_data(self):
        """Test exporting user data for GDPR compliance"""
        # Set up user with data
        self.test_user.first_name = "John"
        self.test_user.last_name = "Doe"
        self.test_user.company = "Test Company"
        
        # Create a test website
        website = self.create_test_website(user_id=self.test_user.id)
        
        result = self.service.export_user_data(self.test_user.id)
        
        # Verify export result
        self.assertTrue(result['success'])
        self.assertEqual(result['user_id'], self.test_user.id)
        self.assertIn('export_date', result)
        self.assertIn('data', result)
        
        # Verify exported data structure
        data = result['data']
        self.assertIn('personal_information', data)
        self.assertIn('account_settings', data)
        self.assertIn('privacy_preferences', data)
        self.assertIn('websites', data)
        self.assertIn('usage_statistics', data)
        
        # Verify personal information
        personal_info = data['personal_information']
        self.assertEqual(personal_info['email'], self.test_user.email)
        self.assertEqual(personal_info['first_name'], "John")
        self.assertEqual(personal_info['last_name'], "Doe")
        self.assertEqual(personal_info['company'], "Test Company")
        
        # Verify websites data
        self.assertEqual(len(data['websites']), 1)
        self.assertEqual(data['websites'][0]['url'], website.url)
    
    def test_delete_user_data(self):
        """Test deleting user data for GDPR compliance"""
        # Generate verification token
        verification_token = hashlib.sha256(f"{self.test_user.id}:{self.test_user.email}".encode()).hexdigest()[:16]
        
        # Store original data
        original_email = self.test_user.email
        
        result = self.service.delete_user_data(self.test_user.id, verification_token)
        
        # Verify deletion result
        self.assertTrue(result['success'])
        self.assertEqual(result['user_id'], self.test_user.id)
        self.assertIn('deletion_date', result)
        
        # Verify user data was anonymized
        # Refresh user from database
        from app.models.user import User
        updated_user = User.query.get(self.test_user.id)
        self.assertNotEqual(updated_user.email, original_email)
        self.assertTrue(updated_user.email.startswith('deleted_'))
        self.assertIsNone(updated_user.first_name)
        self.assertIsNone(updated_user.last_name)
        self.assertIsNone(updated_user.company)
        self.assertFalse(updated_user.is_active)
    
    def test_delete_user_data_invalid_token(self):
        """Test deleting user data with invalid verification token"""
        invalid_token = "invalid_token"
        
        result = self.service.delete_user_data(self.test_user.id, invalid_token)
        
        # Should fail with invalid token
        self.assertFalse(result['success'])
        self.assertEqual(result['error'], 'Invalid verification token')
    
    def test_perform_security_audit(self):
        """Test performing comprehensive security audit"""
        # Mock audit methods
        with patch.object(self.service, '_audit_password_strength', return_value=3):
            with patch.object(self.service, '_audit_data_encryption', return_value=2):
                with patch.object(self.service, '_audit_gdpr_compliance', return_value=[
                    {'type': 'missing_consent', 'severity': 'medium'}
                ]):
                    with patch.object(self.service, '_audit_session_security', return_value=[]):
                        with patch.object(self.service, '_audit_api_security', return_value=[]):
                            
                            assessment = self.service.perform_security_audit()
                            
                            # Verify assessment structure
                            self.assertIsInstance(assessment, SecurityAssessment)
                            self.assertIsInstance(assessment.overall_score, float)
                            self.assertIsInstance(assessment.vulnerabilities, list)
                            self.assertIsInstance(assessment.recommendations, list)
                            self.assertIsInstance(assessment.compliance_status, dict)
                            self.assertIsInstance(assessment.last_assessment, datetime)
                            
                            # Verify vulnerabilities were detected
                            self.assertGreater(len(assessment.vulnerabilities), 0)
                            
                            # Check vulnerability types
                            vuln_types = [v['type'] for v in assessment.vulnerabilities]
                            self.assertIn('weak_passwords', vuln_types)
                            self.assertIn('unencrypted_data', vuln_types)
                            self.assertIn('missing_consent', vuln_types)
                            
                            # Verify score calculation
                            self.assertLessEqual(assessment.overall_score, 100)
                            self.assertGreaterEqual(assessment.overall_score, 0)
    
    def test_log_security_event(self):
        """Test logging security events"""
        # Log a test event
        self.service._log_security_event(
            event_type='test_event',
            description='Test security event',
            severity=SecurityLevel.MEDIUM,
            user_id=self.test_user.id,
            metadata={'test': 'data'}
        )
        
        # Verify event was logged
        self.assertEqual(len(self.service.audit_events), 1)
        
        event = self.service.audit_events[0]
        self.assertIsInstance(event, SecurityAuditEvent)
        self.assertEqual(event.event_type, 'test_event')
        self.assertEqual(event.description, 'Test security event')
        self.assertEqual(event.severity, SecurityLevel.MEDIUM)
        self.assertEqual(event.user_id, self.test_user.id)
        self.assertEqual(event.metadata['test'], 'data')
        self.assertIsInstance(event.timestamp, datetime)
        self.assertIsNotNone(event.event_id)
    
    def test_security_policies_configuration(self):
        """Test security policies are properly configured"""
        # Verify password policy
        password_policy = self.service.password_policy
        self.assertEqual(password_policy['min_length'], 12)
        self.assertTrue(password_policy['require_uppercase'])
        self.assertTrue(password_policy['require_lowercase'])
        self.assertTrue(password_policy['require_numbers'])
        self.assertTrue(password_policy['require_special_chars'])
        self.assertEqual(password_policy['max_age_days'], 90)
        self.assertEqual(password_policy['history_count'], 5)
        
        # Verify GDPR settings
        gdpr_settings = self.service.gdpr_settings
        self.assertEqual(gdpr_settings['data_retention_days'], 365 * 3)
        self.assertTrue(gdpr_settings['consent_required'])
        self.assertTrue(gdpr_settings['right_to_be_forgotten'])
        self.assertTrue(gdpr_settings['data_portability'])
        self.assertEqual(gdpr_settings['breach_notification_hours'], 72)
        
        # Verify security thresholds
        thresholds = self.service.security_thresholds
        self.assertEqual(thresholds['failed_login_attempts'], 5)
        self.assertEqual(thresholds['session_timeout_minutes'], 30)
        self.assertEqual(thresholds['api_rate_limit_per_hour'], 1000)
        self.assertEqual(thresholds['suspicious_activity_threshold'], 10)
    
    def test_data_classification_levels(self):
        """Test data classification levels"""
        # Test all classification levels
        classifications = [
            DataClassification.PUBLIC,
            DataClassification.INTERNAL,
            DataClassification.CONFIDENTIAL,
            DataClassification.RESTRICTED
        ]
        
        for classification in classifications:
            result = self.service.encrypt_sensitive_data("test data", classification)
            self.assertTrue(result.success)
            self.assertIsNotNone(result.encrypted_data)
    
    def test_security_levels(self):
        """Test security level classifications"""
        levels = [SecurityLevel.LOW, SecurityLevel.MEDIUM, SecurityLevel.HIGH, SecurityLevel.CRITICAL]
        
        for level in levels:
            # Test logging with different security levels
            self.service._log_security_event(
                event_type='test',
                description=f'Test {level.value} event',
                severity=level
            )
        
        # Verify all events were logged
        self.assertEqual(len(self.service.audit_events), 4)
        
        # Verify different severity levels
        severities = [event.severity for event in self.service.audit_events]
        self.assertEqual(set(severities), set(levels))


if __name__ == '__main__':
    unittest.main()
