"""
Comprehensive Unit Tests for Sprint 13: Premium Features Development
Tests premium tier features, advanced AI analysis, priority matching, white-label options, and payment processing
"""
import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os
from datetime import datetime, timedelta
import json

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from tests.framework.base_test import BaseTestCase, ServiceTestCase, APITestCase
from app.services.premium_features_service import PremiumFeaturesService, PremiumTier, PremiumFeatureAccess
from app.services.payment_service import PaymentService, PaymentResult, PricingPlan
from app.models.user import User


class TestPremiumFeaturesService(ServiceTestCase):
    """Test cases for Premium Features Service"""
    
    @property
    def service_class(self):
        return PremiumFeaturesService
    
    def setUp(self):
        super().setUp()
        self.test_user_free = self.create_test_user(plan='free')
        self.test_user_pro = self.create_test_user(email='<EMAIL>', plan='pro')
        self.test_user_agency = self.create_test_user(email='<EMAIL>', plan='agency')
    
    def test_get_user_tier(self):
        """Test getting user's premium tier"""
        # Test free tier
        tier = self.service.get_user_tier(self.test_user_free.id)
        self.assertEqual(tier, PremiumTier.FREE)
        
        # Test pro tier
        tier = self.service.get_user_tier(self.test_user_pro.id)
        self.assertEqual(tier, PremiumTier.PRO)
        
        # Test agency tier
        tier = self.service.get_user_tier(self.test_user_agency.id)
        self.assertEqual(tier, PremiumTier.AGENCY)
        
        # Test non-existent user
        tier = self.service.get_user_tier(99999)
        self.assertEqual(tier, PremiumTier.FREE)
    
    def test_feature_access_configuration(self):
        """Test feature access configuration for different tiers"""
        # Free tier access
        free_access = self.service.get_feature_access(self.test_user_free.id)
        self.assertFalse(free_access.advanced_ai_analysis)
        self.assertFalse(free_access.priority_matching)
        self.assertFalse(free_access.white_label_customization)
        self.assertTrue(free_access.api_access)  # Basic API access
        
        # Pro tier access
        pro_access = self.service.get_feature_access(self.test_user_pro.id)
        self.assertTrue(pro_access.advanced_ai_analysis)
        self.assertTrue(pro_access.priority_matching)
        self.assertFalse(pro_access.white_label_customization)
        self.assertTrue(pro_access.advanced_reporting)
        
        # Agency tier access
        agency_access = self.service.get_feature_access(self.test_user_agency.id)
        self.assertTrue(agency_access.advanced_ai_analysis)
        self.assertTrue(agency_access.priority_matching)
        self.assertTrue(agency_access.white_label_customization)
        self.assertTrue(agency_access.dedicated_support)
    
    def test_has_feature_access(self):
        """Test checking specific feature access"""
        # Free user should not have premium features
        self.assertFalse(self.service.has_feature_access(self.test_user_free.id, 'advanced_ai_analysis'))
        self.assertFalse(self.service.has_feature_access(self.test_user_free.id, 'priority_matching'))
        
        # Pro user should have pro features
        self.assertTrue(self.service.has_feature_access(self.test_user_pro.id, 'advanced_ai_analysis'))
        self.assertTrue(self.service.has_feature_access(self.test_user_pro.id, 'priority_matching'))
        self.assertFalse(self.service.has_feature_access(self.test_user_pro.id, 'white_label_customization'))
        
        # Agency user should have all features
        self.assertTrue(self.service.has_feature_access(self.test_user_agency.id, 'advanced_ai_analysis'))
        self.assertTrue(self.service.has_feature_access(self.test_user_agency.id, 'white_label_customization'))
    
    def test_require_premium_feature(self):
        """Test premium feature requirement enforcement"""
        # Free user should be denied premium features
        with self.assertRaises(PermissionError):
            self.service.require_premium_feature(self.test_user_free.id, 'advanced_ai_analysis')
        
        # Pro user should have access to pro features
        self.assertTrue(self.service.require_premium_feature(self.test_user_pro.id, 'advanced_ai_analysis'))
        
        # Pro user should be denied agency features
        with self.assertRaises(PermissionError):
            self.service.require_premium_feature(self.test_user_pro.id, 'white_label_customization')
    
    def test_advanced_ai_analysis(self):
        """Test advanced AI analysis functionality"""
        test_content = "This is a test article about SEO and content marketing strategies."
        
        # Test with pro user
        result = self.service.get_advanced_ai_analysis(self.test_user_pro.id, test_content)
        
        # Verify analysis structure
        self.assertIn('basic_analysis', result)
        self.assertIn('advanced_sentiment', result)
        self.assertIn('topic_modeling', result)
        self.assertIn('readability_advanced', result)
        self.assertIn('seo_optimization', result)
        self.assertIn('competitive_insights', result)
        self.assertIn('content_gaps', result)
        self.assertIn('improvement_suggestions', result)
        
        # Verify basic analysis
        basic = result['basic_analysis']
        self.assertGreater(basic['word_count'], 0)
        self.assertGreater(basic['character_count'], 0)
        
        # Verify sentiment analysis
        sentiment = result['advanced_sentiment']
        self.assertIn('overall_sentiment', sentiment)
        self.assertIn('sentiment_score', sentiment)
        self.assertIn('emotion_analysis', sentiment)
        
        # Test with free user (should raise permission error)
        with self.assertRaises(PermissionError):
            self.service.get_advanced_ai_analysis(self.test_user_free.id, test_content)
    
    def test_priority_matching(self):
        """Test priority matching functionality"""
        website = self.create_test_website(user_id=self.test_user_pro.id)
        
        # Test with pro user
        matches = self.service.get_priority_matching(self.test_user_pro.id, website.id, limit=10)
        
        # Verify matches structure
        self.assertIsInstance(matches, list)
        self.assertLessEqual(len(matches), 10)
        
        if matches:
            match = matches[0]
            self.assertIn('website_id', match)
            self.assertIn('domain', match)
            self.assertIn('relevance_score', match)
            self.assertIn('authority_score', match)
            self.assertIn('premium_score', match)
            self.assertIn('priority_level', match)
            self.assertIn('estimated_value', match)
            
            # Verify premium enhancements
            self.assertIn(match['priority_level'], ['high', 'medium', 'low'])
            self.assertIn('estimated_seo_value', match['estimated_value'])
        
        # Test with free user (should raise permission error)
        with self.assertRaises(PermissionError):
            self.service.get_priority_matching(self.test_user_free.id, website.id)
    
    def test_white_label_configuration(self):
        """Test white-label configuration functionality"""
        # Test with agency user
        config = self.service.get_white_label_config(self.test_user_agency.id)
        
        # Verify configuration structure
        self.assertIn('branding', config)
        self.assertIn('domain', config)
        self.assertIn('features', config)
        self.assertIn('integrations', config)
        
        # Verify branding options
        branding = config['branding']
        self.assertIn('brand_name', branding)
        self.assertIn('brand_colors', branding)
        self.assertIn('primary', branding['brand_colors'])
        
        # Verify domain options
        domain = config['domain']
        self.assertIn('subdomain', domain)
        self.assertTrue(domain['subdomain'].endswith('.linkup.com'))
        
        # Verify feature customization
        features = config['features']
        self.assertTrue(features['hide_linkup_branding'])
        self.assertTrue(features['custom_email_templates'])
        
        # Test with pro user (should raise permission error)
        with self.assertRaises(PermissionError):
            self.service.get_white_label_config(self.test_user_pro.id)


class TestPaymentService(ServiceTestCase):
    """Test cases for Payment Service"""
    
    @property
    def service_class(self):
        return PaymentService
    
    def setUp(self):
        super().setUp()
        self.test_user = self.create_test_user()
    
    def test_pricing_plans_configuration(self):
        """Test pricing plans configuration"""
        plans = self.service.get_pricing_plans()
        
        # Verify all expected plans exist
        expected_plans = ['pro', 'agency', 'enterprise']
        for plan_id in expected_plans:
            self.assertIn(plan_id, plans)
            
            plan = plans[plan_id]
            self.assertIsInstance(plan, PricingPlan)
            self.assertEqual(plan.plan_id, plan_id)
            self.assertGreater(plan.price_monthly, 0)
            self.assertGreater(plan.price_yearly, 0)
            self.assertIsInstance(plan.features, list)
            self.assertIsInstance(plan.limits, dict)
            
            # Verify yearly pricing is discounted
            yearly_equivalent = plan.price_monthly * 12
            self.assertLess(plan.price_yearly, yearly_equivalent)
    
    def test_get_plan_details(self):
        """Test getting specific plan details"""
        # Test valid plan
        pro_plan = self.service.get_plan_details('pro')
        self.assertIsNotNone(pro_plan)
        self.assertEqual(pro_plan.plan_id, 'pro')
        self.assertEqual(pro_plan.name, 'Pro')
        
        # Test invalid plan
        invalid_plan = self.service.get_plan_details('invalid')
        self.assertIsNone(invalid_plan)
    
    @patch('stripe.Customer.create')
    def test_create_customer(self, mock_stripe_create):
        """Test Stripe customer creation"""
        # Mock Stripe response
        mock_customer = Mock()
        mock_customer.id = 'cus_test123'
        mock_stripe_create.return_value = mock_customer
        
        # Test customer creation
        customer_id = self.service.create_customer(
            self.test_user.id, 
            self.test_user.email, 
            'Test User'
        )
        
        # Verify customer creation
        self.assertEqual(customer_id, 'cus_test123')
        mock_stripe_create.assert_called_once()
        
        # Verify user was updated
        updated_user = User.query.get(self.test_user.id)
        self.assertEqual(updated_user.stripe_customer_id, 'cus_test123')
    
    @patch('stripe.Subscription.create')
    @patch('stripe.Customer.create')
    def test_create_subscription(self, mock_customer_create, mock_subscription_create):
        """Test subscription creation"""
        # Mock Stripe responses
        mock_customer = Mock()
        mock_customer.id = 'cus_test123'
        mock_customer_create.return_value = mock_customer
        
        mock_subscription = Mock()
        mock_subscription.id = 'sub_test123'
        mock_subscription.current_period_end = 1640995200  # 2022-01-01
        mock_subscription.latest_invoice.payment_intent.id = 'pi_test123'
        mock_subscription_create.return_value = mock_subscription
        
        # Test subscription creation
        result = self.service.create_subscription(self.test_user.id, 'pro', 'monthly')
        
        # Verify result
        self.assertTrue(result.success)
        self.assertEqual(result.subscription_id, 'sub_test123')
        self.assertEqual(result.transaction_id, 'pi_test123')
        
        # Verify user plan was updated
        updated_user = User.query.get(self.test_user.id)
        self.assertEqual(updated_user.plan, 'pro')
        self.assertIsNotNone(updated_user.plan_expires_at)
    
    @patch('stripe.Subscription.list')
    @patch('stripe.Subscription.delete')
    def test_cancel_subscription(self, mock_subscription_delete, mock_subscription_list):
        """Test subscription cancellation"""
        # Set up user with Stripe customer ID
        self.test_user.stripe_customer_id = 'cus_test123'
        self.test_user.plan = 'pro'
        
        # Mock Stripe responses
        mock_subscription = Mock()
        mock_subscription.id = 'sub_test123'
        mock_subscription_list.return_value.data = [mock_subscription]
        
        mock_cancelled_subscription = Mock()
        mock_cancelled_subscription.id = 'sub_test123'
        mock_subscription_delete.return_value = mock_cancelled_subscription
        
        # Test cancellation
        result = self.service.cancel_subscription(self.test_user.id)
        
        # Verify result
        self.assertTrue(result.success)
        self.assertEqual(result.subscription_id, 'sub_test123')
        
        # Verify user plan was downgraded
        updated_user = User.query.get(self.test_user.id)
        self.assertEqual(updated_user.plan, 'free')
        self.assertIsNone(updated_user.plan_expires_at)
    
    def test_subscription_status_free_user(self):
        """Test subscription status for free user"""
        status = self.service.get_subscription_status(self.test_user.id)
        
        self.assertEqual(status['status'], 'free')
        self.assertEqual(status['plan'], 'free')
        self.assertIsNone(status['expires_at'])
        self.assertTrue(status['is_active'])
    
    @patch('stripe.Subscription.list')
    def test_subscription_status_paid_user(self, mock_subscription_list):
        """Test subscription status for paid user"""
        # Set up user with subscription
        self.test_user.stripe_customer_id = 'cus_test123'
        self.test_user.plan = 'pro'
        
        # Mock Stripe response
        mock_subscription = Mock()
        mock_subscription.status = 'active'
        mock_subscription.id = 'sub_test123'
        mock_subscription.current_period_end = 1640995200  # 2022-01-01
        mock_subscription.items.data = [Mock()]
        mock_subscription.items.data[0].price.recurring.interval = 'month'
        mock_subscription_list.return_value.data = [mock_subscription]
        
        # Test status
        status = self.service.get_subscription_status(self.test_user.id)
        
        self.assertEqual(status['status'], 'active')
        self.assertEqual(status['plan'], 'pro')
        self.assertTrue(status['is_active'])
        self.assertEqual(status['subscription_id'], 'sub_test123')
        self.assertEqual(status['billing_cycle'], 'monthly')


class TestPremiumAPIRoutes(APITestCase):
    """Test cases for Premium API routes"""
    
    @property
    def base_url(self):
        return '/api/premium'
    
    def setUp(self):
        super().setUp()
        self.test_user_pro = self.create_test_user(email='<EMAIL>', plan='pro')
    
    def test_get_feature_access(self):
        """Test getting feature access endpoint"""
        # Test with authenticated user
        with self.app.test_client() as client:
            # Mock JWT token
            with patch('flask_jwt_extended.get_jwt_identity', return_value=self.test_user_pro.id):
                response = client.get(f'{self.base_url}/features')
                
                self.assertEqual(response.status_code, 200)
                data = json.loads(response.data)
                
                self.assertTrue(data['success'])
                self.assertIn('tier', data['data'])
                self.assertIn('features', data['data'])
                self.assertEqual(data['data']['tier'], 'pro')
    
    def test_advanced_analysis_endpoint(self):
        """Test advanced analysis endpoint"""
        test_data = {
            'content': 'This is a test article about SEO strategies.',
            'analysis_type': 'comprehensive'
        }
        
        with self.app.test_client() as client:
            with patch('flask_jwt_extended.get_jwt_identity', return_value=self.test_user_pro.id):
                response = client.post(
                    f'{self.base_url}/analysis/advanced',
                    data=json.dumps(test_data),
                    content_type='application/json'
                )
                
                self.assertEqual(response.status_code, 200)
                data = json.loads(response.data)
                
                self.assertTrue(data['success'])
                self.assertIn('analysis', data['data'])
                self.assertIn('content_length', data['data'])
    
    def test_priority_matching_endpoint(self):
        """Test priority matching endpoint"""
        website = self.create_test_website(user_id=self.test_user_pro.id)
        test_data = {
            'website_id': website.id,
            'limit': 20
        }
        
        with self.app.test_client() as client:
            with patch('flask_jwt_extended.get_jwt_identity', return_value=self.test_user_pro.id):
                response = client.post(
                    f'{self.base_url}/matching/priority',
                    data=json.dumps(test_data),
                    content_type='application/json'
                )
                
                self.assertEqual(response.status_code, 200)
                data = json.loads(response.data)
                
                self.assertTrue(data['success'])
                self.assertIn('matches', data['data'])
                self.assertIn('total_matches', data['data'])
    
    def test_pricing_plans_endpoint(self):
        """Test pricing plans endpoint"""
        with self.app.test_client() as client:
            response = client.get(f'{self.base_url}/subscription/plans')
            
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            
            self.assertTrue(data['success'])
            self.assertIn('plans', data['data'])
            self.assertIn('currency', data['data'])
            
            # Verify plan structure
            plans = data['data']['plans']
            self.assertIn('pro', plans)
            self.assertIn('agency', plans)
            
            pro_plan = plans['pro']
            self.assertIn('name', pro_plan)
            self.assertIn('price_monthly', pro_plan)
            self.assertIn('price_yearly', pro_plan)
            self.assertIn('features', pro_plan)
            self.assertIn('savings_yearly', pro_plan)


if __name__ == '__main__':
    unittest.main()
